# miniapps/utils/mailbox_subscriptions.py
# -*- coding: utf-8 -*-

"""miniapps/utils/mailbox_subscriptions.py（已改造）

1. 支持多微信小程序 AppID：动态按 AppID 获取 / 缓存 access_token。
2. send_subscribe_message 新增 `appid` 参数（可空，空时走旧逻辑）。
3. send_like_notification 会遍历全部已配置 AppID，直到成功或全部失败；保证作者来自第二个小程序也能收到通知。

保持向下兼容：如果环境中只配置一套变量或调用方仍按老签名调用，行为与旧版一致。
"""

import requests
import time
import os
import logging
import json
import datetime
import pymysql

# --- 新增：从其他模块导入 ---
from config.connection_pools import get_mailbox_db_connection
# --- 导入结束 ---

# 配置一个独立的 logger，或者可以考虑从主应用传递 logger 对象
logger = logging.getLogger(__name__)

# ------------------------------------------------------------------
# 多 AppID 配置与 access_token 缓存
# ------------------------------------------------------------------

# 从环境变量读取两套（或更多）微信小程序配置；第二套变量名以 _2 结尾
# 如需更多套，可自行按同规则扩展 _3、_4…

WECHAT_APP_CONFIG = {}

def _load_wechat_config():
    """在首次导入时加载环境变量到 WECHAT_APP_CONFIG"""
    global WECHAT_APP_CONFIG

    def _add(appid_key_suffix):
        appid_env = f"MAILBOX_WECHAT_APPID{appid_key_suffix}"
        secret_env = f"MAILBOX_WECHAT_SECRET{appid_key_suffix}"
        tmpl_env = f"MAILBOX_WECHAT_LIKE_TEMPLATE_ID{appid_key_suffix}"

        appid = os.getenv(appid_env)
        secret = os.getenv(secret_env)
        tmpl = os.getenv(tmpl_env)

        if appid and secret:
            WECHAT_APP_CONFIG[appid] = {
                "secret": secret,
                "tmpl_like": tmpl
            }

    # 主配置使用空后缀
    _add("")
    # 第二套（可选）
    _add("_2")


_load_wechat_config()

# access_token 缓存改为按 appid 维度
access_token_cache = {}  # {appid: {"token": str, "expires_at": timestamp}}

class SubscriptionError(Exception):
    """自定义订阅消息错误基类"""
    pass

class WeChatAPIError(SubscriptionError):
    """微信API返回错误"""
    pass

class NetworkRequestError(SubscriptionError):
    """网络请求错误"""
    pass

class JSONParsingError(SubscriptionError):
    """JSON解析错误"""
    pass

# ------------------------------------------------------------------
# 函数：按 AppID 获取 access_token（带缓存）
# ------------------------------------------------------------------

def get_access_token(appid):
    """获取或刷新指定 AppID 的 access_token，带缓存"""
    if appid not in WECHAT_APP_CONFIG:
        raise ValueError(f"未知的 AppID：{appid}，请检查 WECHAT_APP_CONFIG")

    now = time.time()
    cache_entry = access_token_cache.get(appid, {})

    if cache_entry and cache_entry.get("token") and cache_entry.get("expires_at", 0) > now + 300:
        logger.debug(f"使用缓存的 Access Token（appid={appid}）")
        return cache_entry["token"]

    secret = WECHAT_APP_CONFIG[appid]["secret"]

    url = (
        "https://api.weixin.qq.com/cgi-bin/token"
        f"?grant_type=client_credential&appid={appid}&secret={secret}"
    )

    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()

        if "access_token" in data and "expires_in" in data:
            token_val = data["access_token"]
            expires_at_val = now + data["expires_in"]
            access_token_cache[appid] = {
                "token": token_val,
                "expires_at": expires_at_val
            }
            logger.info(
                f"获取新的 Access Token 成功（appid={appid}），有效期至: "
                f"{time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expires_at_val))}"
            )
            return token_val
        else:
            errcode = data.get("errcode", "N/A")
            errmsg = data.get("errmsg", "未知错误")
            raise WeChatAPIError(
                f"获取 Access Token 失败（appid={appid}）: errcode={errcode}, errmsg={errmsg}"
            )
    except requests.exceptions.RequestException as e:
        raise NetworkRequestError(f"网络错误（appid={appid}）: {e}")
    except json.JSONDecodeError as e:
        raise JSONParsingError(f"解析 JSON 出错（appid={appid}）: {e}")

# --- 发送订阅消息函数 ---
def send_subscribe_message(touser, template_id, data, page=None, appid=None):
    """发送微信小程序订阅消息

    新增参数 appid：指定使用哪一个小程序的 token 发送；
    为空时默认取 WECHAT_APP_CONFIG 的第一项（向下兼容）。
    """
    # 选择 appid
    if not appid:
        # 取字典第一项保证兼容旧调用
        appid = next(iter(WECHAT_APP_CONFIG)) if WECHAT_APP_CONFIG else None

    if not appid or appid not in WECHAT_APP_CONFIG:
        raise SubscriptionError("未提供有效的 AppID，无法发送订阅消息")

    try:
        access_token = get_access_token(appid)

        url = (
            "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?"
            f"access_token={access_token}"
        )
        payload = {"touser": touser, "template_id": template_id, "data": data}
        if page:
            payload["page"] = page
            # 如果需要跳转到小程序指定版本（开发版/体验版/正式版）
            # payload["miniprogram_state"] = "developer" # 可选 "developer", "trial", "formal"

        headers = {'Content-Type': 'application/json'}
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        result = response.json() # 尝试解析JSON，如果失败会在下面被捕获

        if result.get("errcode") == 0:
            logger.info(
                f"成功发送订阅消息（appid={appid}）给用户 {touser}，模板 ID: {template_id}"
            )
        else:
            errcode = result.get('errcode', 'N/A')
            errmsg = result.get('errmsg', '未知错误')
            detailed_error = (
                f"微信 API 错误: errcode={errcode}, errmsg='{errmsg}' "
                f"(用户: {touser}, 模板ID: {template_id}, appid={appid})"
            )
            logger.error(f"发送订阅消息失败: {detailed_error}")
            raise WeChatAPIError(detailed_error)
            
    except WeChatAPIError: # 捕获微信API错误并直接重新抛出
        raise
    except NetworkRequestError: # 捕获网络请求错误并直接重新抛出
        raise
    except JSONParsingError: # 捕获JSON解析错误并直接重新抛出
        raise
    except ValueError as ve: # 捕获 get_access_token 中 AppID/Secret 未配置的错误
        logger.error(f"发送订阅消息前置错误: {str(ve)} (用户: {touser}, 模板ID: {template_id})")
        raise SubscriptionError(f"发送订阅消息前置错误: {str(ve)}") # 包装为通用订阅错误
    except requests.exceptions.RequestException as e: # requests.post 可能的网络错误
        network_error_detail = (
            f"发送订阅消息时网络错误: {str(e)} (用户: {touser}, 模板ID: {template_id}, appid={appid})"
        )
        logger.error(network_error_detail)
        raise NetworkRequestError(network_error_detail)
    except json.JSONDecodeError as e: # response.json() 可能的解析错误
        json_error_detail = (
            f"解析微信响应时JSON错误: {str(e)} (用户: {touser}, 模板ID: {template_id}, appid={appid})"
        )
        logger.error(json_error_detail)
        raise JSONParsingError(json_error_detail)
    except Exception as e: # 捕获所有其他未预期的异常
        # 只有当异常不是我们已经定义的特定类型时，才记录为"未预期错误"
        if not isinstance(e, SubscriptionError):
            logger.error(
                f"发送订阅消息过程中发生未分类的未预期错误: {str(e)} "
                f"(用户: {touser}, 模板ID: {template_id}, appid={appid})"
            )
        raise # 重新抛出，让调用方 (toggle_message_like) 处理并记录到数据库

# --- 新增：点赞通知主函数 ---
def send_like_notification(
    author_user_id,
    author_name,
    message_content_snippet,
    liker_nickname,
    like_id,
):
    """
    处理发送点赞通知的完整流程。
    包括：获取作者信息、检查平台、准备数据、发送通知、记录结果到数据库。
    """
    # 1. 查询作者的 openid 和 platform
    conn_author = get_mailbox_db_connection()
    author_info = None
    try:
        with conn_author.cursor() as cursor_author:
             cursor_author.execute("SELECT openid, platform FROM users WHERE id = %s", (author_user_id,))
             author_info = cursor_author.fetchone()
             if not author_info:
                logger.warning(f"未找到留言 {message_content_snippet} 的作者信息 (作者ID: {author_user_id})，无法发送通知。")
                return
    except Exception as e_author:
        logger.error(f"查询作者信息失败 (作者ID: {author_user_id}): {e_author}")
        return # 如果无法获取作者信息，则提前退出
    finally:
        if conn_author:
            conn_author.close()

    # 2. 检查平台是否为微信
    if not author_info or author_info.get("platform") != "WX":
        logger.info(
            f"作者 (ID: {author_user_id}) 非微信平台用户，无需发送订阅消息。"
        )
        return

    # 3. 准备基础数据
    author_openid = author_info["openid"]

    anonymous_liker_nickname = "不愿透露姓名的匿名用户"
    current_time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
    page_path = f"pages/view/view?name={author_name}"

    logger.info(
        f"准备发送点赞通知给用户 {author_openid} (作者ID: {author_user_id})，"
        f"点赞者: {anonymous_liker_nickname}, 关联点赞ID: {like_id}"
    )

    # 4. 遍历所有已配置 AppID，直到发送成功（或全部失败）
    subscription_note_content = None
    for appid, cfg in WECHAT_APP_CONFIG.items():
        template_id = cfg.get("tmpl_like") or os.getenv("MAILBOX_WECHAT_LIKE_TEMPLATE_ID")
        if not template_id:
            logger.warning(
                f"AppID={appid} 未配置点赞模板 ID，跳过本次尝试。"
            )
            continue

        try:
            # 根据模板字段差异构造数据
            if appid == os.getenv("MAILBOX_WECHAT_APPID_2"):
                # 第二小程序模板：thing4, time2, thing3, thing6
                notification_data = {
                    "thing4": {"value": message_content_snippet},
                    "time2": {"value": current_time_str},
                    "thing3": {"value": anonymous_liker_nickname},
                    "thing6": {"value": "点击查看"}
                }
            else:
                # 默认（第一个小程序）模板
                notification_data = {
                    "thing5": {"value": message_content_snippet},
                    "time2": {"value": current_time_str},
                    "thing3": {"value": anonymous_liker_nickname},
                }

            send_subscribe_message(
                touser=author_openid,
                template_id=template_id,
                data=notification_data,
                page=page_path,
                appid=appid,
            )
            current_time_for_note = datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            subscription_note_content = (
                f"[成功] {current_time_for_note}: 通过 appid={appid} 发送订阅消息成功。"
            )
            logger.info(
                f"点赞ID {like_id}: 订阅消息发送成功 (appid={appid})。"
            )
            break  # 成功即停止
        except SubscriptionError as se:
            logger.warning(
                f"点赞ID {like_id}: 通过 appid={appid} 发送失败: {se}，尝试下一个 AppID。"
            )
            continue
        except Exception as notify_error:
            logger.error(
                f"点赞ID {like_id}: 通过 appid={appid} 发送出现未知错误: {notify_error}"
            )

    # 若循环结束仍未成功
    if not subscription_note_content:
        subscription_note_content = (
            f"在所有 AppID 上发送订阅消息均失败 ({datetime.datetime.now()})。"
        )

    # 4. 将发送结果更新到 message_likes 表中
    if subscription_note_content and like_id is not None:
        conn_notes = None
        try:
            conn_notes = get_mailbox_db_connection()
            with conn_notes.cursor() as cursor_notes:
                cursor_notes.execute(
                    "UPDATE message_likes SET subscription_notes = %s WHERE id = %s",
                    (subscription_note_content, like_id),
                )
                conn_notes.commit()
                logger.info(f"点赞ID {like_id}: 成功更新订阅消息发送状态到数据库。")
        except pymysql.Error as db_notes_error:
            logger.error(f"点赞ID {like_id}: 更新订阅消息状态到数据库失败: {db_notes_error}")
            if conn_notes:
                conn_notes.rollback()
        except Exception as e_notes:
            logger.error(f"点赞ID {like_id}: 更新订阅消息状态时发生未知错误: {e_notes}")
        finally:
            if conn_notes:
                conn_notes.close()
# --- 新增函数结束 ---

# 可以添加一个启动时测试函数（如果需要）
# def send_test_subscribe_message_on_startup():
#     test_openid = "o-cOr4hdybWZnDyKe4pLpFh5E-l4" # 替换为你的测试 OpenID
#     test_template_id = "eETaVQLDJnPY-1y6wgmKt_G0yAnfHnOdT3PDDOtAau8" # 替换为你的测试模板 ID
#     test_data = {
#         "thing5": {"value": "你的测试留言被点赞啦！"},
#         "time2": {"value": time.strftime('%Y-%m-%d %H:%M:%S')},
#         "thing3": {"value": "测试点赞用户"}
#     }
#     test_page = "pages/index/index" # 测试跳转页面
#     logger.info("尝试在启动时发送测试订阅消息...")
#     send_subscribe_message(test_openid, test_template_id, test_data, test_page) 