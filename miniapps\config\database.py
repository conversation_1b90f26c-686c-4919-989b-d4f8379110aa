# config/database.py
import pymysql
import logging
import re
import json
import os
from dotenv import load_dotenv
import colorlog # 导入 colorlog

# 移除顶层导入
# from .db_utils import validate_db_config, initialize_database 

# 加载环境变量
load_dotenv()

def setup_logger():
    """设置日志记录器配置"""
    # 配置日志
    # log_level = logging.INFO
    log_level = logging.DEBUG   # 生产环境使用 INFO 级别 | 修改为 DEBUG 以查看详细日志 
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # 创建彩色格式化器
    color_formatter = colorlog.ColoredFormatter(
        '%(log_color)s' + log_format,
        log_colors={
            'DEBUG':    'cyan',
            'INFO':     'green',
            'WARNING':  'yellow',
            'ERROR':    'red',
            'CRITICAL': 'red,bg_white',
        },
        secondary_log_colors={},
        style='%'
    )

    # 创建标准格式化器（用于文件）
    plain_formatter = logging.Formatter(log_format)

    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除可能存在的默认处理器（如果直接运行脚本可能没有）
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # 创建文件处理器
    file_handler = logging.FileHandler('database.log', encoding='utf-8')
    file_handler.setFormatter(plain_formatter)
    root_logger.addHandler(file_handler)

    # 创建控制台处理器（使用彩色格式化器）
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(color_formatter)
    root_logger.addHandler(stream_handler)

    # 返回当前模块的日志记录器
    return logging.getLogger(__name__)

# 设置日志 (确保在 db_utils 导入前定义)
logger = setup_logger()

# 定义全局配置变量，初始为None (确保在 db_utils 导入前定义)
MAILBOX_DB_CONFIG = None
PRESS_BUTTON_DB_CONFIG = None

def log_info(message):
    """统一的信息日志记录函数"""
    logger.info(f" {message}")

def log_warning(message):
    """统一的警告日志记录函数"""
    logger.warning(f"⚠️ {message}")

def log_error(message):
    """统一的错误日志记录函数"""
    logger.error(f"❌ {message}")

def log_success(message):
    """统一的成功日志记录函数"""
    logger.info(f" {message}")

def load_db_config(env_var_name, config_name):
    """加载数据库配置 (确保在 db_utils 导入前定义)"""
    try:
        config_json = os.getenv(env_var_name)
        if not config_json:
            raise ValueError(f"环境变量 {env_var_name} 未设置")
        
        config = json.loads(config_json)
        db_config = {
            **config,
            'cursorclass': pymysql.cursors.DictCursor
        }
        logger.info(f" 成功加载{config_name}数据库配置")
        return db_config
        
    except (json.JSONDecodeError, ValueError, TypeError) as e:
        logger.error(f"❌ 从环境变量加载{config_name}数据库配置失败: {str(e)}")
        return None

# 从环境变量读取数据库配置并解析JSON (确保在 db_utils 导入前赋值)
MAILBOX_DB_CONFIG = load_db_config('MAILBOX_DB_CONFIG', '匿名留言')
PRESS_BUTTON_DB_CONFIG = load_db_config('PRESS_BUTTON_DB_CONFIG', '按钮')

# 匿名留言小程序表结构定义 (确保在 db_utils 导入前定义)
MAILBOX_TABLES = {
    'users': """
        CREATE TABLE IF NOT EXISTS users (
          id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
          openid VARCHAR(255) NOT NULL,
          nickname VARCHAR(255) NOT NULL,
          avatar_url VARCHAR(255) NOT NULL,
          platform ENUM('WX', 'QQ', 'BOT') NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          is_banned TINYINT(1) DEFAULT 0 COMMENT '是否被禁用，0-正常，1-禁用',
          remark TEXT COMMENT '用户备注信息', 
          last_login_time DATETIME COMMENT '用户上一次登录时间',
          updated_at DATETIME ON UPDATE CURRENT_TIMESTAMP COMMENT '用户信息更新时间',
          qq_unionid VARCHAR(255) NULL DEFAULT NULL COMMENT 'QQ平台UnionID',
          wx_unionid VARCHAR(255) NULL DEFAULT NULL COMMENT '微信平台UnionID',
          phone_number VARCHAR(50) NULL DEFAULT NULL COMMENT '用户手机号',
          UNIQUE (openid, platform)
        ) COMMENT '用户表，用于存储用户的基本信息'
    """,
    'anonymous_messages': """
        CREATE TABLE IF NOT EXISTS anonymous_messages (
          id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          content TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          user_id INT NOT NULL,
          like_count INT DEFAULT 0 COMMENT '点赞数量',
          show_author TINYINT(1) DEFAULT 0 COMMENT '是否显示作者信息, 0-不显示, 1-显示',
          FOREIGN KEY (user_id) REFERENCES users(id)
        ) COMMENT '匿名留言表，记录用户发送的匿名留言'
    """,
    'drift_bottles': """
        CREATE TABLE IF NOT EXISTS drift_bottles (
          id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
          content TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          user_id INT NOT NULL,
          nickname VARCHAR(255) NOT NULL,
          avatar_url VARCHAR(255) NOT NULL,
          FOREIGN KEY (user_id) REFERENCES users(id)
        ) COMMENT '漂流瓶表，记录用户投放的漂流瓶及其内容'
    """,
    'message_likes': """
        CREATE TABLE IF NOT EXISTS message_likes (
          id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          message_id INT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          subscription_notes TEXT NULL COMMENT '记录订阅消息发送状态和错误日志',
          FOREIGN KEY (user_id) REFERENCES users(id),
          FOREIGN KEY (message_id) REFERENCES anonymous_messages(id) ON DELETE CASCADE,
          UNIQUE KEY (user_id, message_id)
        ) COMMENT '留言点赞表，记录用户对留言的点赞记录'
    """
}

# 按钮小程序表结构定义 (确保在 db_utils 导入前定义)
PRESS_BUTTON_TABLES = {
    'users': """
        CREATE TABLE IF NOT EXISTS users (
          id INT AUTO_INCREMENT PRIMARY KEY,
          openid VARCHAR(255) UNIQUE NOT NULL,
          nickname VARCHAR(255),
          avatar_url TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """,
    'scenarios': """
        CREATE TABLE IF NOT EXISTS scenarios (
          id INT AUTO_INCREMENT PRIMARY KEY,
          scenario_condition TEXT NOT NULL,
          result TEXT NOT NULL,
          press INT DEFAULT 0,
          decline INT DEFAULT 0,
          created_by VARCHAR(255)
        )
    """,
    'comments': """
        CREATE TABLE IF NOT EXISTS comments (
          id INT AUTO_INCREMENT PRIMARY KEY,
          scenario_id INT,
          content TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          user_id INT,
          parent_id INT,
          FOREIGN KEY (scenario_id) REFERENCES scenarios(id),
          FOREIGN KEY (user_id) REFERENCES users(id),
          FOREIGN KEY (parent_id) REFERENCES comments(id)
        )
    """,
    'user_choices': """
        CREATE TABLE IF NOT EXISTS user_choices (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT,
          scenario_id INT,
          choice ENUM('press', 'decline'),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users(id),
          FOREIGN KEY (scenario_id) REFERENCES scenarios(id),
          UNIQUE KEY user_scenario (user_id, scenario_id)
        )
    """
}

# 预期的表结构字段定义 (确保在 db_utils 导入前定义)
EXPECTED_TABLE_STRUCTURE = {
    'mailbox': {
        'users': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'openid', 'Type': 'varchar(255)', 'Null': 'NO'},
            {'Field': 'nickname', 'Type': 'varchar(255)', 'Null': 'NO'},
            {'Field': 'avatar_url', 'Type': 'varchar(255)', 'Null': 'NO'},
            {'Field': 'platform', 'Type': "enum('WX','QQ','BOT')", 'Null': 'NO'},
            {'Field': 'created_at', 'Type': 'datetime', 'Default': 'CURRENT_TIMESTAMP'},
            {'Field': 'is_banned', 'Type': 'tinyint(1)', 'Default': '0'},
            {'Field': 'remark', 'Type': 'text', 'Null': 'YES'},
            {'Field': 'last_login_time', 'Type': 'datetime', 'Null': 'YES', 'Comment': '用户上一次登录时间'},
            {'Field': 'updated_at', 'Type': 'datetime', 'Null': 'YES', 'OnUpdate': 'CURRENT_TIMESTAMP', 'Comment': '用户信息更新时间'},
            {'Field': 'qq_unionid', 'Type': 'varchar(255)', 'Null': 'YES', 'Default': None, 'Comment': 'QQ平台UnionID'},
            {'Field': 'wx_unionid', 'Type': 'varchar(255)', 'Null': 'YES', 'Default': None, 'Comment': '微信平台UnionID'},
            {'Field': 'phone_number', 'Type': 'varchar(50)', 'Null': 'YES', 'Default': None, 'Comment': '用户手机号'}
        ],
        'anonymous_messages': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'name', 'Type': 'varchar(255)', 'Null': 'NO'},
            {'Field': 'content', 'Type': 'text', 'Null': 'NO'},
            {'Field': 'created_at', 'Type': 'datetime', 'Default': 'CURRENT_TIMESTAMP'},
            {'Field': 'user_id', 'Type': 'int', 'Null': 'NO', 'Key': 'MUL'},
            {'Field': 'like_count', 'Type': 'int', 'Default': '0', 'Comment': '点赞数量'},
            {'Field': 'show_author', 'Type': 'tinyint(1)', 'Default': '0', 'Comment': '是否显示作者信息, 0-不显示, 1-显示'}
        ],
        'drift_bottles': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'content', 'Type': 'text', 'Null': 'NO'},
            {'Field': 'created_at', 'Type': 'datetime', 'Default': 'CURRENT_TIMESTAMP'},
            {'Field': 'user_id', 'Type': 'int', 'Null': 'NO', 'Key': 'MUL'},
            {'Field': 'nickname', 'Type': 'varchar(255)', 'Null': 'NO'},
            {'Field': 'avatar_url', 'Type': 'varchar(255)', 'Null': 'NO'}
        ],
        'message_likes': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'user_id', 'Type': 'int', 'Null': 'NO', 'Key': 'MUL'},
            {'Field': 'message_id', 'Type': 'int', 'Null': 'NO', 'Key': 'MUL'},
            {'Field': 'created_at', 'Type': 'datetime', 'Default': 'CURRENT_TIMESTAMP'},
            {'Field': 'subscription_notes', 'Type': 'text', 'Null': 'YES', 'Comment': '记录订阅消息发送状态和错误日志'},
            {'Field': 'remark', 'Type': 'text', 'Null': 'YES', 'Comment': '备注'}
        ]
    },
    'pressthebutton': {
        'users': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'openid', 'Type': 'varchar(255)', 'Null': 'NO', 'Key': 'UNI'},
            {'Field': 'nickname', 'Type': 'varchar(255)', 'Null': 'YES'},
            {'Field': 'avatar_url', 'Type': 'text', 'Null': 'YES'},
            {'Field': 'created_at', 'Type': 'timestamp', 'Default': 'CURRENT_TIMESTAMP'}
        ],
        'scenarios': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'scenario_condition', 'Type': 'text', 'Null': 'NO'},
            {'Field': 'result', 'Type': 'text', 'Null': 'NO'},
            {'Field': 'press', 'Type': 'int', 'Default': '0'},
            {'Field': 'decline', 'Type': 'int', 'Default': '0'},
            {'Field': 'created_by', 'Type': 'varchar(255)', 'Null': 'YES'}
        ],
        'comments': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'scenario_id', 'Type': 'int', 'Null': 'YES', 'Key': 'MUL'},
            {'Field': 'content', 'Type': 'text', 'Null': 'NO'},
            {'Field': 'created_at', 'Type': 'timestamp', 'Default': 'CURRENT_TIMESTAMP'},
            {'Field': 'user_id', 'Type': 'int', 'Null': 'YES', 'Key': 'MUL'},
            {'Field': 'parent_id', 'Type': 'int', 'Null': 'YES', 'Key': 'MUL'}
        ],
        'user_choices': [
            {'Field': 'id', 'Type': 'int', 'Null': 'NO', 'Key': 'PRI', 'Extra': 'auto_increment'},
            {'Field': 'user_id', 'Type': 'int', 'Null': 'YES', 'Key': 'MUL'},
            {'Field': 'scenario_id', 'Type': 'int', 'Null': 'YES', 'Key': 'MUL'},
            {'Field': 'choice', 'Type': "enum('press','decline')", 'Null': 'YES'},
            {'Field': 'created_at', 'Type': 'timestamp', 'Default': 'CURRENT_TIMESTAMP'}
        ]
    }
}

# 表的预期索引和外键关系 (确保在 db_utils 导入前定义)
# 注意：当前 db_utils.py 中的自动表结构更新功能仅支持添加缺失字段，
# 无法自动创建缺失的索引。
# 如果添加了新的字段并期望为其创建索引（如下面的 idx_qq_unionid 等），
# 除了在此处定义 EXPECTED_INDEXES 外，还需要手动连接数据库执行相应的 CREATE INDEX 语句。
# 例如，对于 mailbox 数据库的 users 表，需要执行：
# USE mailbox;
# CREATE INDEX idx_qq_unionid ON users (qq_unionid);
# CREATE INDEX idx_wx_unionid ON users (wx_unionid);
# CREATE INDEX idx_phone_number ON users (phone_number);
EXPECTED_INDEXES = {
    'mailbox': {
        'users': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'openid', 'Column_name': 'openid', 'Non_unique': 0},
            {'Key_name': 'openid', 'Column_name': 'platform', 'Non_unique': 0},
            {'Key_name': 'idx_qq_unionid', 'Column_name': 'qq_unionid', 'Non_unique': 1},
            {'Key_name': 'idx_wx_unionid', 'Column_name': 'wx_unionid', 'Non_unique': 1},
            {'Key_name': 'idx_phone_number', 'Column_name': 'phone_number', 'Non_unique': 1}
        ],
        'anonymous_messages': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'user_id', 'Column_name': 'user_id', 'Non_unique': 1}
        ],
        'drift_bottles': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'user_id', 'Column_name': 'user_id', 'Non_unique': 1}
        ],
        'message_likes': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'user_id', 'Column_name': 'user_id', 'Non_unique': 1},
            {'Key_name': 'message_id', 'Column_name': 'message_id', 'Non_unique': 1},
            {'Key_name': 'user_id_2', 'Column_name': 'user_id', 'Non_unique': 0},
            {'Key_name': 'user_id_2', 'Column_name': 'message_id', 'Non_unique': 0}
        ]
    },
    'pressthebutton': {
        'users': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'openid', 'Column_name': 'openid', 'Non_unique': 0}
        ],
        'scenarios': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0}
        ],
        'comments': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'scenario_id', 'Column_name': 'scenario_id', 'Non_unique': 1},
            {'Key_name': 'user_id', 'Column_name': 'user_id', 'Non_unique': 1},
            {'Key_name': 'parent_id', 'Column_name': 'parent_id', 'Non_unique': 1}
        ],
        'user_choices': [
            {'Key_name': 'PRIMARY', 'Column_name': 'id', 'Non_unique': 0},
            {'Key_name': 'user_scenario', 'Column_name': 'user_id', 'Non_unique': 0},
            {'Key_name': 'user_scenario', 'Column_name': 'scenario_id', 'Non_unique': 0},
            {'Key_name': 'scenario_id', 'Column_name': 'scenario_id', 'Non_unique': 1}
        ]
    }
}

def get_db_config(db_name):
    """根据数据库名称获取对应的数据库配置 (确保在 db_utils 导入前定义)"""
    if db_name == 'mailbox':
        return MAILBOX_DB_CONFIG
    elif db_name == 'pressthebutton':
        return PRESS_BUTTON_DB_CONFIG
    else:
        logger.error(f"❌ 不支持的数据库名称: {db_name}")
        return None

def create_db_connection(db_config):
    """创建数据库连接 (确保在 db_utils 导入前定义)"""
    try:
        connection = pymysql.connect(
            host=db_config['host'],
            user=db_config['user'],
            password=db_config['password'],
            charset=db_config['charset'],
            cursorclass=pymysql.cursors.DictCursor
        )
        logger.info(f" 成功连接到MySQL服务器 ({db_config['host']})")
        return connection
    except Exception as e:
        logger.error(f"❌ 连接MySQL服务器失败: {str(e)}")
        return None

# ----- 函数定义结束，以下代码块用于在模块导入时执行初始化 -----

# 当这个模块被导入时自动初始化数据库
if __name__ != "__main__":
    # 检查是否在 Werkzeug reloader 的主进程中
    if os.environ.get("WERKZEUG_RUN_MAIN") == "true":
        # 在需要时才导入，避免循环依赖
        try:
            from .db_utils import validate_db_config, initialize_database
            
            logger.info("🚀 数据库配置模块已加载 (运行在 Werkzeug 子进程)，准备初始化数据库结构...")
            # 先验证数据库配置
            if validate_db_config(): 
                initialize_database()
            else:
                logger.error("❌ 数据库配置验证失败，无法初始化数据库")
        except ImportError as e:
             logger.error(f"❌ 导入 db_utils 失败: {e}")
    else:
        logger.info("🚀 数据库配置模块已加载 (运行在 Werkzeug 主进程或非 reloader 环境)")
