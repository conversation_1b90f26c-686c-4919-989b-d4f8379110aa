// #ifndef VUE3
// 非Vue3情况下的相关代码，注意是“非”
import Vue from 'vue'
import App from './App'

Vue.config.productionTip = false


// 定义全局的基础 URL
const baseUrl = 'https://mini.hxlsf.com';
// const baseUrl = 'http://127.0.0.1:5000';
// 将基础 URL 挂载到 Vue 的原型上
Vue.prototype.$baseUrl = baseUrl;

/**
 * 平台区分标识（通过条件编译实现）
 * 
 * 说明：
 * 1. 这里使用的是uni-app的条件编译功能，在编译时确定平台类型
 * 2. 虽然 #ifdef 指令看起来像普通注释，但它们实际上是特殊的预处理指令
 * 3. 这些指令会在编译阶段被uni-app编译器识别并处理，而不是被当作注释忽略
 * 4. 编译器会根据目标平台保留或移除相应代码块
 *    - 当编译为微信小程序时，只保留 #ifdef MP-WEIXIN 和 #endif 之间的代码
 *    - 当编译为QQ小程序时，只保留 #ifdef MP-QQ 和 #endif 之间的代码
 * 5. 编译后的实际代码已经没有其他平台的代码，只包含当前平台需要的代码
 * 6. 这种方式不依赖运行时API调用，在编译阶段就已确定，更加高效
 */
let platform = 'QQ'; // 默认为QQ小程序
// #ifdef MP-WEIXIN
platform = 'WX'; // 微信小程序环境
// #endif
// #ifdef MP-QQ
platform = 'QQ'; // QQ小程序环境
// #endif

// 输出当前平台标识，便于开发调试
console.log('当前使用的平台标识：', platform);

// 将平台变量挂载到 Vue 的原型上
Vue.prototype.$platform = platform;

// 新增: 全局Mixin，保证任意组件都能取到 $baseUrl/$platform
Vue.mixin({
  beforeCreate() {
    if (!this.$baseUrl) {
      this.$baseUrl = Vue.prototype.$baseUrl;
    }
    if (!this.$platform) {
      this.$platform = Vue.prototype.$platform;
    }
  }
});

App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
// #endif




// ——————————————————————————————————



// #ifdef VUE3
// Vue3 相关代码
import { createSSRApp } from 'vue'
import App from './App.vue'

// 定义全局的基础 URL
// const baseUrlVue3 = 'https://mini.hxlsf.com';
const baseUrlVue3 = 'http://127.0.0.1:5000';

/**
 * 平台区分标识（通过条件编译实现）
 * 
 * 说明：
 * 1. 这里使用的是uni-app的条件编译功能，在编译时确定平台类型
 * 2. 虽然 #ifdef 指令看起来像普通注释，但它们实际上是特殊的预处理指令
 * 3. 这些指令会在编译阶段被uni-app编译器识别并处理，而不是被当作注释忽略
 * 4. 编译器会根据目标平台保留或移除相应代码块
 *    - 当编译为微信小程序时，只保留 #ifdef MP-WEIXIN 和 #endif 之间的代码
 *    - 当编译为QQ小程序时，只保留 #ifdef MP-QQ 和 #endif 之间的代码
 * 5. 编译后的实际代码已经没有其他平台的代码，只包含当前平台需要的代码
 * 6. 这种方式不依赖运行时API调用，在编译阶段就已确定，更加高效
 */
let platformVue3 = 'QQ'; // 默认为QQ小程序
// #ifdef MP-WEIXIN
platformVue3 = 'WX'; // 微信小程序环境
// #endif
// #ifdef MP-QQ
platformVue3 = 'QQ'; // QQ小程序环境
// #endif

// 输出当前平台标识，便于开发调试
console.log('当前使用的平台标识：', platformVue3);

export function createApp() {
  const app = createSSRApp(App)
  
  // 将基础 URL 挂载到全局属性上
  app.config.globalProperties.$baseUrl = baseUrlVue3;
  // 将平台变量挂载到全局属性上
  app.config.globalProperties.$platform = platformVue3;
  
  // 在Vue3分支中也添加相同的mixin
  app.mixin({
    beforeCreate() {
      if (!this.$baseUrl) {
        this.$baseUrl = app.config.globalProperties.$baseUrl;
      }
      if (!this.$platform) {
        this.$platform = app.config.globalProperties.$platform;
      }
    }
  });
  
  return {
    app
  }
}
// #endif