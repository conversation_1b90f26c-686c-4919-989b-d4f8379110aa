<!-- index.vue -->
<template>
  <view class="container">
    <!-- 延迟加载背景元素 -->
    <template v-if="showBackgroundEffects">
      <!-- 新的背景元素：动态渐变光晕 -->
      <view class="gradient-blob blob-1"></view>
      <view class="gradient-blob blob-2"></view>
      <view class="gradient-blob blob-3"></view>

      <!-- 保留的背景网格图案 -->
      <view class="decorative-pattern"></view>
    </template>

    <view class="content">
      <view class="header">
        <text class="title" v-if="$platform === 'QQ'">悄悄话留言信</text>
        <text class="title" v-else-if="$platform === 'WX'">传话留言信</text>
        <text class="typing-text">{{ currentText }}</text>
        <!-- 显示总留言数 -->
        <view class="total-messages">
          <text class="total-prefix">这里封存着</text>
          <text class="total-number">{{totalMessages}}</text>
          <text class="total-suffix">封情书待签收</text>
        </view>
      </view>

      <!-- 添加广告组件，仅在微信平台显示 -->
      <ad-custom 
        v-if="$platform === 'WX' && adUnitId" 
        :unit-id="adUnitId" 
        @load="adLoad" 
        @error="adError" 
        @close="adClose"
      ></ad-custom>

      <view class="search-bar">
        <view class="search-input-wrapper">
          <input
            class="search-input"
            placeholder="输入名字查看留言"
            v-model="searchName"
            :disabled="is_banned"
          />
        </view>
        <button
          class="search-button"
          @tap="is_banned ? showBannedTip() : viewMessages()"
        >
          <view class="search-icon"></view>
          <text class="button-text">看留言</text>
        </button>
      </view>

      <button
        class="write-button"
        @tap="writeMessage()"
      >
        <view class="write-icon"></view>
        <text class="button-text">写留言</text>
      </button>

      <!-- 最近留言展示区 -->
      <view class="recent-messages">
        <view class="section-header">
          <text class="section-title">最新留言</text>
          <text class="section-subtitle"> " 那些说不出口的话，就留在这吧~ " </text>
        </view>

        <!-- 加载状态 -->
        <view v-if="isLoading" class="message-loading">
          <!-- 三个骨架屏元素 -->
          <view class="skeleton-item" v-for="i in 3" :key="i">
            <view class="skeleton-header">
              <view class="skeleton-recipient skeleton-shimmer-base"></view>
              <view class="skeleton-time skeleton-shimmer-base"></view>
            </view>
            <view class="skeleton-content skeleton-shimmer-base"></view>
            <view class="skeleton-content-short skeleton-shimmer-base"></view>
          </view>
        </view>

        <!-- 留言列表，仅在非加载状态且有消息时显示 -->
        <view v-else-if="recentMessages.length > 0" class="message-list">
          <view
            class="message-item"
            v-for="(message, index) in recentMessages"
            :key="index"
            :class="{ 'message-appear': showAnimation }"
            :style="{ animationDelay: `${index * 0.2}s` }"
          >
            <!-- 收信人信息和互动栏 -->
            <view class="message-header">
              <view class="recipient-wrapper">
                <image class="user-avatar" :src="formatAvatarUrl(message.author_info ? message.author_info.avatarUrl : '')" mode="aspectFill"></image>
                <text class="recipient-prefix">给</text>
                <text class="recipient-name">{{formatName(message.name)}}</text>
                <text class="recipient-suffix">的一封信</text>
              </view>
              <view class="message-meta">
                <!-- 互换位置：先显示时间，再显示点赞按钮 -->
                <text class="message-time">{{formatTime(message.created_at)}}</text>
                <view
                  class="message-like-btn"
                  @tap.stop="toggleLike(message)"
                  :class="{ 'liked': message.is_liked_by_user }"
                >
                  <view class="like-icon"></view>
                  <text class="like-count">{{message.like_count || 0}}</text>
                </view>
              </view>
            </view>

            <!-- 信件内容预览 -->
            <view class="message-content-wrapper">
              <text class="message-text">{{message.content}}</text>
            </view>
          </view>
        </view>

        <!-- 新增：即使有留言也显示提示语 -->
        <view v-if="recentMessages.length > 0" class="bottom-hint-container">
          <text class="bottom-hint-text">一张素笺静待佳音，心事在此落款～</text>
        </view>

        <!-- 空状态提示，仅在非加载状态且无消息时显示 -->
        <view v-else class="empty-state">
          <view class="empty-illustration"></view>
          <text class="empty-text">肯定有你的，快去输入名字看看吧～</text>
        </view>
      </view>
    </view>

    <!-- 公告弹窗 -->
    <view class="notice-popup-overlay" v-if="showNoticePopup" :class="{ closing: isNoticeClosing }" @touchmove.stop.prevent>
      <view class="notice-popup-content" :class="{ closing: isNoticeClosing }">
        <text class="notice-title">{{ noticeData.title }}</text>
        <image
          v-if="noticeData.imageUrl"
          :src="noticeData.imageUrl"
          class="notice-image"
          mode="widthFix"
          @tap="previewImage(noticeData.imageUrl)"
        />
        <text class="notice-body">{{ noticeData.body }}</text>
        <button class="notice-close-button" @tap="closeNoticePopup">
          我知道了
        </button>
      </view>
    </view>

    <!-- 用户通知弹窗 -->
    <view class="notice-popup-overlay" v-if="showUserNotice" :class="{ closing: isUserNoticeClosing }" @touchmove.stop.prevent>
      <view class="notice-popup-content user-notice-popup" :class="{ closing: isUserNoticeClosing }">
        <text class="notice-title">{{ userNoticeData.title }}</text>
        <image
          v-if="userNoticeData.imageUrl"
          :src="userNoticeData.imageUrl"
          class="notice-image"
          mode="widthFix"
          @tap="previewImage(userNoticeData.imageUrl)"
        />
        <text class="notice-body">{{ userNoticeData.body }}</text>
        <button class="notice-close-button user-notice-button" @tap="closeUserNotice">
          我知道了
        </button>
      </view>
    </view>
  </view>
</template>

<script>
// 在脚本顶部添加开发环境标识
const IS_DEV = process.env.NODE_ENV !== 'production';

export default {
  data() {
    return {
      searchName: '',
      recentMessages: [],

      // 新增广告相关数据
      appId: '', // 存储小程序的appId
      accountInfo: null, // 存储完整的账号信息
      adUnitId: '', // 广告单元ID

      // 添加新的数据属性
      texts: [],
      currentText: "",
      currentIndex: 0,
      isDeleting: false,
      typingSpeed: 150,
      deletingSpeed: 50,
      pauseTime: 2000,

      totalMessages: 0,  // 实际的总留言数

      // 添加公告弹窗相关数据
      noticeData: null, // 存储公告数据
      showNoticePopup: false, // 控制弹窗显示
      isNoticeClosing: false, // 控制弹窗关闭动画

      // 添加用户通知相关数据
      userNoticeData: null, // 存储用户通知数据
      showUserNotice: false, // 控制用户通知弹窗显示
      isUserNoticeClosing: false, // 控制用户通知弹窗关闭动画

      // 添加加载状态控制
      isLoading: true, // 控制加载状态显示

      // 新增状态用于延迟加载背景动画
      showBackgroundEffects: false,

      // 添加动画控制状态
      showAnimation: false,

      // 添加用户拉黑状态
      is_banned: false,
      banRemark: '',

      // 分享配置
      shareConfig: {
        title: '悄悄话留言信，写下无人知晓的告白~', // 默认标题
        imageUrl: '/static/share-cover.jpg', // 默认图片
      },

      // 点赞频率限制
      likeActionTimestamps: [], // 记录点赞操作时间戳
      RATE_LIMIT_WINDOW: 10000, // 时间窗口：10秒 (毫秒)
      RATE_LIMIT_COUNT: 5,      // 窗口内允许的最大次数
    }
  },

  mounted() {
    // 将token验证逻辑移至optimizedInitApp方法中，避免重复验证
    // 不再在此处获取token，避免重复日志

    // 背景动画效果延迟显示，提高首屏加载速度
    setTimeout(() => {
      this.showBackgroundEffects = true;
    }, 300);
    // 延后加载非首屏关键数据，减少首屏并发请求
    this.loadDeferredData();
  },

  onLoad() {
    // 调整初始化App
    this.optimizedInitApp();
    
    // 获取小程序appId
    this.getWxAppId();
  },

  onShow() {
    // 只在需要时才重新获取消息，避免每次显示都请求
    if (this.recentMessages.length === 0 && !this.isLoading) {
      this.fetchRecentMessages();
    }
  },

  onUnload() {
    // 清除打字机定时器
    if (this._typingTimer) {
      clearTimeout(this._typingTimer);
      this._typingTimer = null; // 显式置空
    }
  },

  onShareAppMessage() {
    return this.getShareConfig();
  },

  onShareTimeline() {
    return this.getShareConfig();
  },

  methods: {
    // 新增：统一调试日志方法
    devLog(...args) {
      if (IS_DEV) {
        // eslint-disable-next-line no-console
        console.log(...args);
      }
    },

    // 新增：获取微信小程序appId 中将 console.log 替换为 devLog
    getWxAppId() {
      // 仅在微信平台执行
      if (this.$platform === 'WX') {
        try {
          const accountInfo = uni.getAccountInfoSync();
          this.accountInfo = accountInfo;
          this.devLog('获取到的账号完整信息:', accountInfo);
          // 保存完整账号信息以便查看
          this.accountInfo = accountInfo;
          // 在控制台打印完整数据
          console.log('获取到的账号完整信息:', accountInfo);
          
                      // 提取appId
            if (accountInfo && accountInfo.miniProgram) {
              this.appId = accountInfo.miniProgram.appId;
              console.log('当前小程序appId:', this.appId);
              
              // 定义广告映射表（使用真实 appId 字符串）
              const AD_UNIT_MAP = {
                // 传话留言信小程序（正式版）
                'wx9e5fd56e48b060d4': 'adunit-407ea15d5391a1b5',
                // 第二个留言小程序
                'wxc9fdf77d1f64ef5a': 'adunit-327aea86910c13f5'
              };

              // 直接通过映射表获取广告单元ID
              if (AD_UNIT_MAP[this.appId]) {
                this.adUnitId = AD_UNIT_MAP[this.appId];
                this.devLog('匹配广告配置成功, appId:', this.appId, '广告单元ID:', this.adUnitId);
              } else {
                this.devLog('未匹配到广告配置, appId:', this.appId);
                this.adUnitId = '';
              }
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('获取小程序账号信息失败:', error);
        }
      }
    },
    
    // 新增：广告事件处理方法
    adLoad() {
      console.log('原生模板广告加载成功, 广告单元ID:', this.adUnitId);
    },
    adError(err) {
      console.error('原生模板广告加载失败, 广告单元ID:', this.adUnitId, '错误信息:', err);
    },
    adClose() {
      console.log('原生模板广告关闭, 广告单元ID:', this.adUnitId);
    },
    
    // 初始化应用
    async optimizedInitApp() {
      // 调试：自动请求后端 headers
      this.fetchDebugHeaders();

      // 从全局获取token
      this.token = getApp().globalData.token || uni.getStorageSync('mailbox_token');
      // 并行获取首屏必需数据（文字、总数、最新留言）
      await Promise.all([
        this.fetchTypingTexts(),
        this.fetchTotalMessages(),
        this.fetchRecentMessages()
      ]).catch(err => {
        // eslint-disable-next-line no-console
        console.error('加载首屏数据时发生错误:', err);
      });
    },

    // 新增：延后加载的次要数据（公告、分享配置、用户通知）
    loadDeferredData() {
      Promise.allSettled([
        this.fetchNotice(),
        this.fetchShareConfig('index'),
        this.fetchUserNotice()
      ]).catch(err => {
        // eslint-disable-next-line no-console
        console.error('加载次要数据发生错误:', err);
      });
    },

    // 新增：调试函数——请求 /mailbox/debug/headers 并打印结果
    async fetchDebugHeaders() {
      try {
        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/debug/headers`,
          method: 'GET'
        });
        this.devLog('[DebugHeaders]', res.data);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error('[DebugHeaders] 请求失败:', e);
      }
    },

    // 处理token过期
    async handleTokenExpired() {
      const app = getApp();
      this.token = null; // 清空本地 token，强制重新登录

      try {
        await app.ensureLogin();
        this.token = app.globalData.token;
        return true; // 刷新成功
      } catch (e) {
        console.error('刷新 Token 失败:', e);
        uni.showToast({
          title: '登录已失效，请稍后重试',
          icon: 'none'
        });
        return false;
      }
    },

    // --- 新增：获取公告信息的方法 ---
    async fetchNotice() {
      try {
        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/notice?platform=${this.$platform}`,
          method: 'GET'
        });

        if (res.statusCode === 200 && res.data && res.data.show === true) {
          const notice = res.data;
          // 拼接完整的图片URL
          if (notice.image_path) {
            notice.imageUrl = this.$baseUrl + notice.image_path;
          }
          this.noticeData = notice;

          // 移除已读判断，直接显示公告
          this.showNoticePopup = true;

        } else if (res.statusCode !== 200) {
          console.error('获取公告失败:', res.data ? res.data.error : '服务器响应错误');
        } else {
          // show为false或数据不完整，不显示弹窗
          console.log('当前无公告或公告数据不完整。');
        }
      } catch (error) {
        console.error('请求公告接口失败:', error);
      }
    },
    // --- 公告信息方法结束 ---

    async fetchTotalMessages() {
        try {
          // 这里我们已经在optimizedInitApp中确保了token的有效性
          // 所以不需要再次验证，直接使用现有的token即可

          const res = await uni.request({
            url: `${this.$baseUrl}/mailbox/total-messages`,
            method: 'GET',
            header: {
              'Authorization': `Bearer ${this.token}`
            }
          });

          if (res.statusCode === 401) {
            // token过期，尝试重新获取
            const success = await this.handleTokenExpired();
            if (success) {
              // 重新调用本方法
              return this.fetchTotalMessages();
            }
            return;
          }

          if (res.data.success) {
            // 在实际数据库总数基础上加上10000
            this.totalMessages = res.data.total + 12000;
          } else {
            console.error('获取总留言数失败:', res.data.error);
          }
        } catch (error) {
          console.error('请求失败:', error);
        }
    },

	// 从后端获取文字内容
    async fetchTypingTexts() {
      try {
        // 移除 ensureToken 调用
        // await this.ensureToken();

        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/typing-texts`,
          method: 'GET',
          // 移除 header
          // header: {
          //   'Authorization': `Bearer ${this.token}`
          // }
        });


        // 直接判断请求是否成功以及数据是否有效
        if (res.statusCode === 200 && res.data.success) {
          this.texts = res.data.data;
          this.startTyping();
        } else {
          console.error('获取文字失败:', res.data ? res.data.error : '服务器响应错误');
        }
      } catch (error) {
        console.error('请求失败:', error);
      }
    },

    async fetchRecentMessages() {
      this.isLoading = true;
      this.showAnimation = false;
      let res; // 提前声明，方便 finally 作用域使用
      try {
        res = await uni.request({
          url: `${this.$baseUrl}/mailbox/messages?page=1&pageSize=5`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        // 处理 Token 过期
        if (res.statusCode === 401) {
          const success = await this.handleTokenExpired();
          if (success) {
            // Token 刷新成功，重新请求
            return this.fetchRecentMessages();
          } else {
            // Token 刷新失败，显示错误提示，不再继续
            uni.showToast({
              title: '获取留言失败，请重试',
              icon: 'none'
            });
            this.isLoading = false; // 结束加载状态
            return; // 明确返回，防止执行 finally
          }
        }

        // 处理成功响应 (注意：数据在 res.data.data 中)
        if (res.statusCode === 200 && res.data && res.data.data) {
          this.recentMessages = res.data.data;
          // 数据加载完成后，设置一个短暂延迟再显示动画
          setTimeout(() => {
            this.showAnimation = true;
          }, 50); // 短暂延迟确保DOM已更新
        } else {
          // 处理其他错误状态码或无效数据
          const errorMsg = res.data && res.data.error ? res.data.error : '加载最近留言失败';
          uni.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('获取最近留言失败:', error);
        uni.showToast({
          title: '无法连接到服务器',
          icon: 'none'
        });
      } finally {
        // 仅在非重试流程中设置 isLoading 为 false
        // (如果因 401 而重试，isLoading 会在重试调用中处理)
        if (!res || res.statusCode !== 401) {
           this.isLoading = false;
        }
      }
    },

    // 优化打字机效果 - 移除 requestAnimationFrame
    startTyping() {
      // 如果没有可用文本，不执行打字效果
      if (!this.texts || this.texts.length === 0) {
        setTimeout(() => this.startTyping(), 500);
        return;
      }

      const current = this.texts[this.currentIndex];
      const currentLength = this.currentText.length;

      if (this.isDeleting) {
        this.currentText = current.substring(0, currentLength - 1);
      } else {
        this.currentText = current.substring(0, currentLength + 1);
      }

      let typeSpeed = this.isDeleting ? this.deletingSpeed : this.typingSpeed;

      if (!this.isDeleting && currentLength === current.length) {
        typeSpeed = this.pauseTime;
        this.isDeleting = true;
      } else if (this.isDeleting && currentLength === 0) {
        this.isDeleting = false;
        this.currentIndex = (this.currentIndex + 1) % this.texts.length;
      }

      // 直接使用 setTimeout，移除 requestAnimationFrame
      this._typingTimer = setTimeout(() => this.startTyping(), typeSpeed);
    },



    formatName(name) {
      if (!name || name.length < 2) return name;
      return name.charAt(0) + 'xx';
    },

    viewMessages() {
      const name = this.searchName.trim();
      // 基本非空检查
      if (!name) {
        uni.showToast({ title: '请输入名字', icon: 'none' });
        return;
      }
      // 关键词屏蔽
      if (name.includes('匿名')) {
        uni.showToast({ title: '输入包含违规关键词', icon: 'none' });
        return;
      }
      // 更严格字符校验：仅允许中文、数字、字母
      const invalidPattern = /[^a-zA-Z0-9\u4e00-\u9fa5]/;
      if (invalidPattern.test(name)) {
        uni.showToast({ title: '搜索名称包含无效字符', icon: 'none' });
        return;
      }
      if (name.length > 20) {
        uni.showToast({ title: '名字长度不能超过20个字符', icon: 'none' });
        return;
      }
      // 通过校验后，写回 searchName（去除首尾空格）
      this.searchName = name;
      uni.navigateTo({
        url: `/pages/view/view?name=${encodeURIComponent(name)}`
      });
    },

    writeMessage() {
      // 如果搜索框中有输入名字，则将名字传递到写留言页面
      const nameParam = this.searchName ? `?name=${encodeURIComponent(this.searchName.trim())}` : '';
      uni.navigateTo({
        url: `/pages/write/write${nameParam}`
      });
    },

    formatTime(timestamp) {
      const date = new Date(timestamp.replace(/-/g, '/'));
      const now = new Date();
      const diff = now - date;
      const minutes = Math.floor(diff / 1000 / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);

      if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 30) {
        return `${days}天前`;
      } else {
        const MM = String(date.getMonth() + 1).padStart(2, '0');
        const dd = String(date.getDate()).padStart(2, '0');
        return `${MM}-${dd}`;
      }
    },

    // 获取分享配置
    fetchShareConfig(pageKey) {
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${this.$baseUrl}/mailbox/share-config?pageKey=${pageKey}`,
          method: 'GET',
          header: this.token ? { 'Authorization': `Bearer ${this.token}` } : {},
          success: (res) => {
            if (res.statusCode === 200 && res.data && res.data.success) {
              // 更新分享配置
              this.shareConfig = {
                title: res.data.title || this.shareConfig.title,
                imageUrl: res.data.imageUrl || this.shareConfig.imageUrl
              };
              resolve(this.shareConfig);
            } else {
              console.error(`获取分享配置请求返回不成功:`, res.data); // 保留错误日志
              reject(new Error('获取分享配置失败'));
            }
          },
          fail: (err) => {
            console.error('获取分享配置失败:', err); // 保留错误日志
            reject(err);
          }
        });
      });
    },

    // 获取分享配置对象
    getShareConfig() {

      // --- 拼接完整的图片 URL ---
      let fullImageUrl = this.shareConfig.imageUrl;
      if (fullImageUrl && fullImageUrl.startsWith('/') && this.$baseUrl) {
        fullImageUrl = this.$baseUrl + fullImageUrl;
      }
      // --- URL 拼接结束 ---

      const shareInfo = {
        title: this.shareConfig.title,
        path: '/pages/index/index',
        imageUrl: fullImageUrl // 使用处理后的 URL
      };
      return shareInfo;
    },

    // --- 新增：获取用户通知的方法 ---
    async fetchUserNotice() {
      try {
        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/user-notice`,
          method: 'GET',
          header: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        // 处理 Token 过期
        if (res.statusCode === 401) {
          const success = await this.handleTokenExpired();
          if (success) {
            // Token 刷新成功，重新请求
            return this.fetchUserNotice();
          }
          return;
        }

        if (res.statusCode === 200 && res.data && res.data.success === true) {
          // 获取到用户通知
          const notice = res.data;

          // 拼接完整的图片URL (如果存在)
          if (notice.imageUrl && notice.imageUrl.startsWith('/')) {
            notice.imageUrl = this.$baseUrl + notice.imageUrl;
          }

          // 保存通知数据
          this.userNoticeData = notice;

          console.log('获取到用户个人通知:', notice);

          // 如果当前没有显示全局公告，则显示用户通知
          // 如果正在显示全局公告，等待其关闭后再显示
          if (!this.showNoticePopup) {
            this.showUserNotice = true;
          } else {
            // 添加一个标记，在全局公告关闭后显示
            this._showUserNoticeAfterGlobal = true;
          }

        } else if (res.statusCode !== 200) {
          console.error('获取用户通知失败:', res.data ? res.data.error : '服务器响应错误');
        } else {
          // 没有需要显示的用户通知
          console.log('当前无用户通知或通知未启用');
        }
      } catch (error) {
        console.error('请求用户通知接口失败:', error);
      }
    },
    // --- 用户通知方法结束 ---

    // --- 新增：关闭用户通知弹窗的方法 ---
    closeUserNotice() {
      this.isUserNoticeClosing = true;
      setTimeout(() => {
        this.showUserNotice = false;
        this.isUserNoticeClosing = false;
      }, 300);
    },

    // --- 修改：关闭公告弹窗的方法，支持关闭后显示用户通知 ---
    closeNoticePopup() {
      this.isNoticeClosing = true;
      setTimeout(() => {
        this.showNoticePopup = false;
        this.isNoticeClosing = false;

        // 检查是否需要在全局公告关闭后显示用户通知
        if (this._showUserNoticeAfterGlobal && this.userNoticeData) {
          this.showUserNotice = true;
          this._showUserNoticeAfterGlobal = false; // 重置标记
        }
      }, 300);
    },

    // 添加显示禁用提示的方法
    showBannedTip() {
      let tipMessage = '您的账号已被禁用';
      if (this.banRemark) {
        tipMessage += `，原因：${this.banRemark}`;
      }

      uni.showToast({
        title: tipMessage,
        icon: 'none',
        duration: 2000
      });
    },

    // 新增：图片预览方法
    previewImage(url) {
      if (!url) return;

      // 防止url为相对路径
      let fullUrl = url;
      if (url.startsWith('/') && this.$baseUrl) {
        fullUrl = this.$baseUrl + url;
      }

      // 调用uni小程序API预览图片
      uni.previewImage({
        urls: [fullUrl], // 需要预览的图片链接列表
        current: fullUrl, // 当前显示图片的链接
        success: () => {
        },
        fail: (err) => {
          console.error('图片预览失败:', err);
          // 预览失败时提示用户
          uni.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },

    // 添加点赞/取消点赞方法
    async toggleLike(message) {
      // 如果用户被禁用，显示提示并阻止操作
      if (this.is_banned) {
        this.showBannedTip();
        return;
      }

      // --- 临时修改：禁止取消点赞 ---
      // 如果消息已经被点赞，则直接返回，不允许取消
      if (message.is_liked_by_user) {
        // console.log('临时：禁止取消点赞'); // 可以取消注释来调试
        uni.showToast({ title: '你已经点过赞啦', icon: 'none' });
        return;
      }
      // --- 临时修改结束 ---

      // --- 频率限制检查 ---
      const now = Date.now();
      // 移除超出时间窗口的时间戳
      this.likeActionTimestamps = this.likeActionTimestamps.filter(
        timestamp => now - timestamp < this.RATE_LIMIT_WINDOW
      );
      // 检查是否达到限制
      if (this.likeActionTimestamps.length >= this.RATE_LIMIT_COUNT) {
        uni.showToast({
          title: '操作太快了，请稍后再试',
          icon: 'none'
        });
        return; // 阻止操作
      }
      // --- 频率限制检查结束 ---

      // 防止频繁点击 (基于单次请求)
      if (message._likeLoading) {
        return;
      }

      // 记录本次操作时间戳
      this.likeActionTimestamps.push(now);

      // 设置加载状态
      this.$set(message, '_likeLoading', true);

      try {
        // 从全局获取token
        if (!this.token) {
          this.token = getApp().globalData.token || uni.getStorageSync('mailbox_token');
          if (!this.token) {
            uni.showToast({ title: '请重启应用重新登录', icon: 'none'});
            return;
          }
        }

        const res = await uni.request({
          url: `${this.$baseUrl}/mailbox/messages/${message.id}/toggle_like`,
          method: 'POST',
          header: {
            'Authorization': `Bearer ${this.token}`
          }
        });

        // 处理 Token 过期
        if (res.statusCode === 401) {
          const success = await this.handleTokenExpired();
          if (success) {
            // 移除加载状态
            this.$set(message, '_likeLoading', false);
            // Token 刷新成功，重新尝试点赞
            this.toggleLike(message);
            return;
          } else {
            uni.showToast({
              title: '登录已失效，请重启应用',
              icon: 'none'
            });
            // Token刷新失败，移除加载状态
            this.$set(message, '_likeLoading', false);
            return; // 明确返回
          }
        }

        // 处理成功响应
        if (res.statusCode === 200 && res.data && res.data.success) {
          // 更新点赞状态和数量
          message.is_liked_by_user = res.data.action === 'liked';
          message.like_count = res.data.like_count;

          // 添加前端日志
          console.log(`[点赞日志] 操作成功: 消息ID ${message.id}, 新状态: ${res.data.action}, 新点赞数: ${res.data.like_count}`);

        } else {
          // 错误处理
          uni.showToast({
            title: res.data.error || '操作失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('点赞操作失败:', error);
        uni.showToast({
          title: '网络异常，请稍后再试',
          icon: 'none'
        });
      } finally {
        // 移除加载状态
        // 使用 $nextTick 确保在DOM更新后移除加载状态，避免快速点击问题
        this.$nextTick(() => {
           this.$set(message, '_likeLoading', false);
        });
      }
    },

    formatAvatarUrl(url) {
      if (!url) {
        return '/static/view-avatar.png';
      }
      if (url.startsWith('http')) {
        return url;
      }
      // 确保 baseUrl 和 url 之间只有一个斜杠
      const baseUrl = this.$baseUrl.endsWith('/') ? this.$baseUrl.slice(0, -1) : this.$baseUrl;
      const avatarPath = url.startsWith('/') ? url : `/${url}`;
      
      return baseUrl + avatarPath;
    },
  }
}
</script>

<style lang="scss">
/* 基础容器样式 */
.container {
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
  background-attachment: fixed;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  // 当前设备状态栏（刘海 / 导航栏）高度
  // padding-top: var(--status-bar-height);
  padding-top: 30rpx;

  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* --- 全新的背景效果 --- */

/* 渐变光晕基础样式 */
.gradient-blob {
  position: fixed;
  border-radius: 50%;
  opacity: 0.4; /* 调整基础不透明度 */
  filter: blur(80rpx);
  z-index: 0;
  will-change: transform, opacity, border-radius; /* 告知浏览器这些属性会变化，优化性能 */
  transform: translateZ(0); /* 强制GPU加速 */
  backface-visibility: hidden; /* 优化动画性能 */
}

/* 第一个光晕 */
.blob-1 {
  width: 500rpx;
  height: 450rpx;
  background: radial-gradient(circle, rgba(255, 111, 145, 0.5) 0%, rgba(254, 172, 94, 0.3) 100%); /* 降低颜色透明度 */
  top: -150rpx;
  left: -100rpx;
  animation: blobMorph 20s cubic-bezier(0.45, 0.05, 0.55, 0.95) infinite alternate;
}

/* 第二个光晕 */
.blob-2 {
  width: 600rpx;
  height: 550rpx;
  background: radial-gradient(circle, rgba(79, 95, 232, 0.4) 0%, rgba(168, 209, 242, 0.2) 100%); /* 降低颜色透明度 */
  bottom: -200rpx;
  right: -150rpx;
  animation: blobMorph 25s cubic-bezier(0.37, 0, 0.63, 1) infinite alternate-reverse 2s;
}

/* 第三个光晕 */
.blob-3 {
  width: 400rpx;
  height: 380rpx;
  background: radial-gradient(circle, rgba(132, 250, 176, 0.4) 0%, rgba(143, 211, 244, 0.2) 100%); /* 降低颜色透明度 */
  top: 30%;
  right: 5%;
  animation: blobMorph 18s cubic-bezier(0.65, 0.05, 0.36, 1) infinite alternate 1s;
}

/* 光晕变形动画 - 降低透明度并增大移动范围 */
@keyframes blobMorph {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
    border-radius: 50% 45% 55% 48%;
    opacity: 0.4; /* 调整动画中的不透明度 */
  }
  25% {
    transform: translate3d(80rpx, -100rpx, 0) scale(1.1) rotate(45deg);
    border-radius: 45% 55% 48% 50%;
    opacity: 0.5; /* 调整动画中的不透明度 */
  }
  50% {
    transform: translate3d(-60rpx, 90rpx, 0) scale(0.9) rotate(-30deg);
    border-radius: 55% 48% 50% 45%;
    opacity: 0.35; /* 调整动画中的不透明度 */
  }
  75% {
    transform: translate3d(90rpx, 120rpx, 0) scale(1.05) rotate(90deg);
    border-radius: 48% 50% 45% 55%;
    opacity: 0.45; /* 调整动画中的不透明度 */
  }
}

/* 优化背景网格图案 */
.decorative-pattern {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-image: radial-gradient(rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px); /* 调暗颜色 */
  background-size: 15rpx 15rpx; /* 调整大小 */
  opacity: 0.6; /* 调整基础不透明度 */
  z-index: 0;
  animation: patternPulse 15s ease-in-out infinite; /* 减慢动画 */
}

/* 背景图案脉冲动画（更柔和） */
@keyframes patternPulse {
  0%, 100% { opacity: 0.4; } /* 调整动画中的不透明度 */
  50% { opacity: 0.25; } /* 调整动画中的不透明度 */
}

/* --- 移除旧的装饰元素动画 --- */
/* @keyframes floatAnimation { ... } */
/* @keyframes rotateAnimation { ... } */


/* --- 其他样式保持不变 --- */
.content {
  padding: 0rpx 30rpx;
  z-index: 1; /* 确保内容在背景之上 */
  position: relative;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  margin-bottom: 12rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 112, 154, 0.7) 0%, rgba(254, 225, 64, 0.7) 100%);
  padding: 52px 0; /* 使用px单位恢复原来的高度 */
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4);
  z-index: -1;
}

.title {
  font-size: 32px; /* 还原为原始大小 */
  font-weight: 700;
  color: #333;
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -10rpx;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #ffa9c6, #a5c7fe);
  border-radius: 3rpx;
  transform: translateX(-50%);
}

.typing-text {
  display: block;
  font-size: 24rpx;
  color: rgba(51, 51, 51, 0.9);
  margin-top: 20rpx;
  height: 32rpx;
  text-align: center;
  font-weight: 500;
}

.total-messages {
  position: absolute;
  bottom: 12rpx;
  left: 12rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.total-prefix, .total-suffix {
  font-size: 20rpx;
  color: rgba(51, 51, 51, 0.85);
  font-weight: 500;
}

.total-number {
  font-weight: bold;
  margin: 0 4rpx;
  font-size: 22rpx;
  display: inline-block;
  min-width: 28rpx;
  text-align: center;
  position: relative;
  padding: 0 2rpx;
  color: #ff6f91;
}

.total-number::after {
  content: '';
  position: absolute;
  bottom: -4rpx;
  left: 0;
  width: 100%;
  height: 2rpx;
  background: #ff6f91;
  opacity: 0.7;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  // margin-bottom: 30rpx;
  padding: 30rpx 0rpx;
}

.search-input-wrapper {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  margin-right: 20rpx;
  padding: 4rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
}

.search-input {
  height: 80rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
  color: #333;
}

.search-button {
  width: 160rpx;
  height: 80rpx;
  border-radius: 30rpx;
  background: linear-gradient(135deg, #ffa9c6, #a5c7fe);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 15rpx rgba(255, 169, 198, 0.25);
  border: none;
}

/* 添加图标 */
.search-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
  margin-right: 8rpx;
}

.search-button:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 8rpx rgba(79, 95, 232, 0.15);
}

.search-button .button-text {
  font-weight: 600;
  font-size: 28rpx;
}

/* 写留言按钮样式 */
.write-button {
  width: 100%;
  height: 90rpx;
  border-radius: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #a8e6cf, #8fd3f4, #84fab0);
  background-size: 300% 100%;
  animation:
      gradientFlow 4s ease infinite,
      scaleAnimation 2s ease-in-out infinite; /* 恢复缩放动效 */
  box-shadow: 0 8rpx 20rpx rgba(132, 250, 176, 0.25);
  border: none;
}

/* 添加图标 */
.write-icon {
  width: 32rpx;
  height: 32rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7'%3E%3C/path%3E%3Cpath d='M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  margin-right: 8rpx;
}

/* 恢复缩放动效 */
@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.10);
  }
  100% {
    transform: scale(1);
  }
}

.write-button:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 10rpx rgba(132, 250, 176, 0.15);
  animation: none;
  background-position: 0% 50%;
}

.write-button .button-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 最近留言区域样式 */
.recent-messages {
  position: relative;
  z-index: 1;
  margin-bottom: 40rpx;
}

.section-header {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.section-subtitle {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 20rpx;
  display: block;
  font-style: italic;
}

.message-list {
  position: relative;
}

.message-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18rpx;
  padding: 24rpx 28rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06), 0 2rpx 6rpx rgba(0, 0, 0, 0.03);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transform: translateY(20rpx);
  opacity: 0;
  position: relative;
  transition: all 0.2s ease;
  overflow: hidden;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04), 0 1rpx 3rpx rgba(0, 0, 0, 0.02);
  }

  &.message-appear {
    animation: messageAppear 0.5s forwards;
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.recipient-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255, 247, 247, 0.5);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.user-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 8rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  background-color: #f0f0f0;
}

.recipient-prefix, .recipient-suffix {
  font-size: 24rpx;
  color: #999;
}

.recipient-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff758c;
  margin: 0 4rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -2rpx;
    width: 100%;
    height: 1rpx;
    background: currentColor;
    opacity: 0.3;
  }
}

/* 右侧元数据区域 - 点赞和时间 */
.message-meta {
  display: flex;
  align-items: center;
}

/* 点赞按钮新设计 - 调整大小 */
.message-like-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 14rpx; /* 增大内边距 */
  border-radius: 40rpx;
  background-color: rgba(249, 249, 249, 0.8);
  position: relative;
  transition: all 0.2s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.03);

  &:active {
    transform: scale(0.92);
  }

  &.liked {
    background-color: rgba(255, 240, 245, 0.9);

    .like-icon {
      background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23FF5A5F" p-id="2878"></path></svg>');
      transform: scale(1.15); /* 增大放大效果 */
    }

    .like-count {
      color: #ff5a5f;
      font-weight: 600;
    }
  }
}

.like-icon {
  width: 32rpx; /* 增大图标大小 */
  height: 32rpx; /* 增大图标大小 */
  background-image: url('data:image/svg+xml;utf8,<svg t="1713265341761" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path d="M512 936.1c-9.1 0-18.2-3.2-25.5-9.6-7.4-6.4-181.5-159.6-289.8-280.1-76.7-85.5-115.5-160.9-115.5-224.5 0-140.4 114.3-254.7 254.7-254.7 65.1 0 126.7 24.8 173.2 69.9 46.5-45.1 108.1-69.9 173.2-69.9 140.4 0 254.7 114.3 254.7 254.7 0 63.6-38.9 139-115.5 224.5-108.3 120.6-282.4 273.7-289.8 280.1-7.4 6.4-16.5 9.6-25.5 9.6z" fill="%23CCCCCC" p-id="2878"></path></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.like-count {
  font-size: 24rpx; /* 增大字体大小 */
  color: #999;
  margin-left: 6rpx; /* 增大左侧间距 */
  min-width: 24rpx; /* 调整最小宽度 */
  text-align: center;
  transition: all 0.2s ease;
}

/* 时间显示改进 */
.message-time {
  font-size: 22rpx;
  color: #aaa;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
  background: rgba(0, 0, 0, 0.03);
  margin-right: 12rpx; /* 添加右侧间距 */
}

/* 内容区域改进 */
.message-content-wrapper {
  padding-left: 12rpx;
  position: relative;
}

.message-text {
  font-size: 28rpx;
  color: #444;
  line-height: 1.6;
  word-break: break-all;

  /* 以下为新增样式，实现两行省略 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制最多显示2行 */
  -webkit-box-orient: vertical;
  white-space: normal; /* 确保能正常换行 */
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.empty-illustration {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z'%3E%3C/path%3E%3Cpolyline points='22,6 12,13 2,6'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
}

.empty-text {
  font-size: 26rpx;
  color: #888;
  text-align: center;
}

/* 公告弹窗样式 - 还原为原始样式 */
.notice-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  animation: fadeIn 0.3s ease forwards;
}

.notice-popup-overlay.closing {
  animation: fadeOut 0.3s ease forwards;
}

.notice-popup-content {
  width: 75%;
  max-width: 320px;
  background-color: #fff; /* 恢复白色背景 */
  border-radius: 15px; /* 恢复圆角 */
  padding: 25px; /* 恢复内边距 */
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* 恢复阴影 */
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: popIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.notice-popup-content.closing {
  animation: popOut 0.3s ease forwards;
}

.notice-title {
  font-size: 20px; /* 恢复字号 */
  font-weight: bold;
  color: #333;
  margin-bottom: 15px; /* 恢复边距 */
  animation: slideDown 0.5s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
  position: relative;
}

.notice-image {
  width: 100%;
  max-height: none; /* 移除最大高度限制，让图片按原比例显示 */
  height: auto; /* 让高度自动调整 */
  object-fit: contain; /* 确保图片完整显示 */
  border-radius: 10px;
  margin-bottom: 15px;
  animation: fadeImage 0.7s ease forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

/* 添加图片点击效果 */
.notice-image:active {
  opacity: 0.8;
  transform: scale(0.98);
  transition: all 0.2s ease;
}

.notice-body {
  font-size: 15px; /* 恢复字号 */
  color: #555;
  line-height: 1.6;
  text-align: left;
  width: 100%;
  margin-bottom: 25px; /* 恢复边距 */
  white-space: pre-wrap;
  animation: fadeText 0.5s ease forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.notice-close-button {
  width: 80%; /* 恢复宽度 */
  height: 40px; /* 恢复高度 */
  background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%); /* 恢复原来的渐变背景 */
  color: #2a9d8f; /* 恢复原来的文字颜色 */
  border: none;
  border-radius: 20px; /* 恢复圆角 */
  font-size: 16px; /* 恢复字号 */
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: buttonAppear 0.5s ease forwards;
  animation-delay: 0.6s;
  opacity: 0;
  transform: translateY(10px); /* 动画保持不变 */
  box-shadow: 0 4px 10px rgba(168, 230, 207, 0.3); /* 恢复原来的阴影 */
}

.notice-close-button:active {
  opacity: 0.9;
  transform: scale(0.98); /* 恢复原来的按压效果 */
  box-shadow: 0 2px 5px rgba(168, 230, 207, 0.2); /* 恢复原来的阴影 */
}

/* 动画关键帧 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes popIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes popOut {
  from {
    transform: scale(1);
    opacity: 1;
   }
  to {
    transform: scale(0.9);
    opacity: 0;
   }
}

@keyframes slideDown {
  from {
    transform: translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeImage {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeText {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes buttonAppear {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-appear {
  animation: messageAppear 0.5s ease forwards;
}

/* 加载动画样式 */
.message-loading {
  padding: 10rpx 0;
}

/* 新增：骨架屏闪烁效果基类 */
.skeleton-shimmer-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
}

.skeleton-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
  position: relative;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.skeleton-recipient {
  width: 150rpx;
  height: 28rpx;
  /* background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); */
  /* background-size: 200% 100%; */
  border-radius: 6rpx;
  /* animation: shimmer 1.5s infinite linear; */ /* 移除重复样式 */
}

.skeleton-time {
  width: 80rpx;
  height: 22rpx;
  /* background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); */
  /* background-size: 200% 100%; */
  border-radius: 6rpx;
  /* animation: shimmer 1.5s infinite linear; */ /* 移除重复样式 */
}

.skeleton-content {
  width: 100%;
  height: 26rpx;
  /* background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); */
  /* background-size: 200% 100%; */
  border-radius: 6rpx;
  margin-bottom: 12rpx;
  /* animation: shimmer 1.5s infinite linear; */ /* 移除重复样式 */
}

.skeleton-content-short {
  width: 70%;
  height: 26rpx;
  /* background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); */
  /* background-size: 200% 100%; */
  border-radius: 6rpx;
  /* animation: shimmer 1.5s infinite linear; */ /* 移除重复样式 */
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 用户通知弹窗自定义样式 */
.user-notice-popup {
  background-color: #fff9f0; /* 稍微区别于公告弹窗的背景色 */
  border: 1px solid rgba(255, 170, 94, 0.3); /* 添加橙色边框 */
}

.user-notice-button {
  background: linear-gradient(135deg, #ffaa5e 0%, #ffd166 100%); /* 使用橙色系渐变 */
  color: #cc6b2c;
  box-shadow: 0 4px 10px rgba(255, 170, 94, 0.3);
}

.user-notice-button:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2px 5px rgba(255, 170, 94, 0.2);
}

/* 新增：底部提示语样式 */
.bottom-hint-container {
  margin-top: 30rpx; /* 调整与上方列表的间距 */
  text-align: center;
  padding: 10rpx 0; /* 调整内边距 */
}

.bottom-hint-text {
  font-size: 24rpx; /* 调整字号使其不过于显眼 */
  color: #888;
  font-style: italic;
}
</style>