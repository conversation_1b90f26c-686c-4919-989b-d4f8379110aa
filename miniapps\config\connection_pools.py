"""
数据库连接池管理模块

本模块集中管理项目中所有的数据库连接池，包括：
1. Mailbox 小程序的连接池
2. 按钮小程序的连接池（未来可能添加）

使用方式:
from config.connection_pools import get_mailbox_db_connection
connection = get_mailbox_db_connection()
# 使用完毕后记得关闭连接
connection.close()
"""
import logging
import pymysql.cursors
from dbutils.pooled_db import PooledDB
from config.database import MAILBOX_DB_CONFIG, PRESS_BUTTON_DB_CONFIG

# 获取日志记录器
logger = logging.getLogger(__name__)

# === Mailbox 小程序的数据库连接池 ===
mailbox_pool = None
try:
    if MAILBOX_DB_CONFIG:  # 确保配置已加载
        mailbox_pool = PooledDB(
            creator=pymysql,  # 使用 PyMySQL 作为数据库驱动
            maxconnections=10,  # 最大连接数
            mincached=2,  # 初始化时创建的最小空闲连接数
            maxcached=5,  # 缓存的最大空闲连接数
            maxshared=3,  # 最大共享连接数（对线程安全的驱动无效）
            blocking=True,  # 连接池满时阻塞等待
            maxusage=None,  # 连接最大复用次数，None表示无限制
            setsession=[],  # 开始会话前执行的命令列表，例如 ['set session wait_timeout=300']
            ping=1,  # 检查连接有效性：0=None, 1=when fetched, 2=when returned, 4=always, 7=all
            host=MAILBOX_DB_CONFIG['host'],
            port=MAILBOX_DB_CONFIG.get('port', 3306),  # 允许端口可选
            user=MAILBOX_DB_CONFIG['user'],
            password=MAILBOX_DB_CONFIG['password'],
            database=MAILBOX_DB_CONFIG['database'],
            charset=MAILBOX_DB_CONFIG.get('charset', 'utf8mb4'),
            cursorclass=pymysql.cursors.DictCursor,
            connect_timeout=MAILBOX_DB_CONFIG.get('connect_timeout', 10),  # 连接超时
            read_timeout=MAILBOX_DB_CONFIG.get('read_timeout', 30),  # 读超时
            write_timeout=MAILBOX_DB_CONFIG.get('write_timeout', 30),  # 写超时
            init_command="SET time_zone = '+08:00'"  # 连接初始化命令
        )
        logger.info("✅ Mailbox数据库连接池初始化成功")
    else:
        logger.error("❌ Mailbox数据库配置未加载，无法初始化连接池")
except Exception as e:
    logger.error(f"❌ Mailbox数据库连接池初始化失败: {str(e)}")
    # 这里不抛出异常，允许应用继续运行
# === Mailbox 连接池初始化结束 ===

# === 按钮小程序的数据库连接池（预留） ===
press_button_pool = None
# 可以在需要时取消注释并实现
# try:
#     if PRESS_BUTTON_DB_CONFIG:
#         press_button_pool = PooledDB(...)
#         logger.info("✅ 按钮小程序数据库连接池初始化成功")
# except Exception as e:
#     logger.error(f"❌ 按钮小程序数据库连接池初始化失败: {str(e)}")
# === 按钮小程序连接池初始化结束 ===

def get_mailbox_db_connection():
    """从Mailbox连接池获取数据库连接"""
    if not mailbox_pool:
        raise ConnectionError("Mailbox数据库连接池不可用")
    try:
        connection = mailbox_pool.connection()
        return connection
    except Exception as e:
        logger.error(f"从Mailbox连接池获取连接失败: {str(e)}")
        raise ConnectionError(f"无法从Mailbox连接池获取数据库连接: {str(e)}")

# 预留按钮小程序的获取连接函数
# def get_press_button_db_connection():
#     """从按钮小程序连接池获取数据库连接"""
#     if not press_button_pool:
#         raise ConnectionError("按钮小程序数据库连接池不可用")
#     try:
#         connection = press_button_pool.connection()
#         return connection
#     except Exception as e:
#         logger.error(f"从按钮小程序连接池获取连接失败: {str(e)}")
#         raise ConnectionError(f"无法从按钮小程序连接池获取数据库连接: {str(e)}")

def close_pools():
    """关闭所有连接池，通常在应用程序退出时调用"""
    try:
        if mailbox_pool:
            mailbox_pool.close()
            logger.info("Mailbox数据库连接池已关闭")
    except Exception as e:
        logger.error(f"关闭Mailbox数据库连接池时出错: {str(e)}")
    
    # 预留按钮小程序的关闭连接池逻辑
    # try:
    #     if press_button_pool:
    #         press_button_pool.close()
    #         logger.info("按钮小程序数据库连接池已关闭")
    # except Exception as e:
    #     logger.error(f"关闭按钮小程序数据库连接池时出错: {str(e)}")
