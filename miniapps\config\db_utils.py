# miniapps/config/db_utils.py
import pymysql
import re
import os

# 从同级目录的 database.py 导入必要的变量和函数
# 确保导入 logger, get_db_config, create_db_connection 以及所有数据结构和配置变量
from .database import (
    logger, 
    get_db_config, 
    create_db_connection, 
    EXPECTED_TABLE_STRUCTURE, 
    EXPECTED_INDEXES,
    MAILBOX_TABLES,
    PRESS_BUTTON_TABLES,
    MAILBOX_DB_CONFIG, # 确保导入配置变量
    PRESS_BUTTON_DB_CONFIG # 确保导入配置变量
)

def standardize_type(type_str):
    """标准化类型字符串以便比较"""
    # 移除多余空格
    type_str = type_str.strip().lower()
    
    # 统一 int 类型表示
    # 修正正则表达式，使用单个反斜杠进行转义
    if re.match(r'^int(?:\(\d+\))?$', type_str):  # 例如 int 或 int(11)
        return 'int'
    # 也可以添加对其他整数类型的处理，例如 bigint
    if re.match(r'^bigint(?:\(\d+\))?$', type_str):
        return 'bigint'
    
    # 统一 timestamp/datetime 默认值
    if 'timestamp' in type_str or 'datetime' in type_str:
        return type_str.split(' ')[0]
    
    return type_str

def auto_update_table_structure():
    """检查是否启用自动更新表结构功能"""
    # 从环境变量读取配置，默认启用
    auto_update = os.getenv('AUTO_UPDATE_TABLE_STRUCTURE', 'True').lower()
    return auto_update in ('true', '1', 'yes')

def create_table(cursor, connection, table_name, create_sql):
    """创建表"""
    logger.info(f"⚙️ 表 {table_name} 不存在，正在创建...")
    try:
        cursor.execute(create_sql)
        connection.commit()
        logger.info(f" 表 {table_name} 创建成功") # 保留原始日志
        return True
    except Exception as e:
        logger.error(f"❌ 创建表 {table_name} 失败: {str(e)}") # 保留原始日志
        return False

def add_missing_field(cursor, table_name, field):
    """添加缺失的字段"""
    try:
        field_name = field['Field']
        field_type = field['Type']
        nullable = "NULL" if field.get('Null', 'YES') == 'YES' else "NOT NULL"
        default_value = f"DEFAULT {field['Default']}" if 'Default' in field and field['Default'] is not None else ""
        extra = field.get('Extra', '')
        
        # 构建ALTER TABLE语句
        alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {field_name} {field_type} {nullable} {default_value} {extra}"
        alter_sql = alter_sql.strip()
        
        logger.warning(f"⚙️ 正在添加缺失字段: {field_name} 到表 {table_name}")
        cursor.execute(alter_sql)
        logger.warning(f" 成功添加字段: {field_name}")
        return True
    except Exception as e:
        logger.error(f"❌ 添加字段失败: {str(e)}") # 保留原始日志 (虽然这是新增函数，但遵循不改日志风格)
        return False

def verify_field(actual_col, expected_col, table_name):
    """验证单个字段的正确性"""
    field_name = expected_col['Field']
    is_correct = True
    
    # 1. 检查字段类型
    expected_type = standardize_type(expected_col['Type'])
    actual_type = standardize_type(actual_col['Type'])
    
    if expected_type != actual_type:
        is_correct = False
        # 注意：这里仍然使用原始类型进行日志记录，以便调试
        logger.error(f"❌ 表 {table_name} 中字段 {field_name} 的类型不匹配: 预期 {expected_col['Type']} (标准化为 {expected_type}), 实际 {actual_col['Type']} (标准化为 {actual_type})")
    else:
        # 如果类型匹配，可以记录一个调试信息（可选）
        logger.debug(f"✅ 表 {table_name} 中字段 {field_name} 的类型匹配: 预期 {expected_col['Type']}, 实际 {actual_col['Type']} (均标准化为 {expected_type})")
    
    # 2. 检查非空约束
    # 预期配置中的 'Null' 应该是 'YES' 或 'NO'
    expected_null = expected_col.get('Null', 'YES').upper()  # 标准化为大写
    actual_null = actual_col.get('Null', 'YES').upper()      # 标准化为大写
    
    if expected_null != actual_null:
        is_correct = False
        logger.error(f"❌ 表 {table_name} 中字段 {field_name} 的非空约束不匹配: 预期 {expected_null}, 实际 {actual_null}")
    
    # 3. 检查默认值
    # 简化默认值比较逻辑，特别是对于 CURRENT_TIMESTAMP
    expected_default_raw = expected_col.get('Default')
    actual_default_raw = actual_col.get('Default')
    
    # 标准化 CURRENT_TIMESTAMP 的表示
    def normalize_default(value):
        if value is None:
            return None  # 保持 None
        val_str = str(value).upper()
        if 'CURRENT_TIMESTAMP' in val_str:
            # 检查是否包含 ON UPDATE CURRENT_TIMESTAMP
            if 'ON UPDATE CURRENT_TIMESTAMP' in val_str:
                return 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            else:
                return 'CURRENT_TIMESTAMP'
        # 对于数字类型，尝试转换为字符串比较，避免整数和字符串 '0' 的问题
        if isinstance(value, (int, float)):
            return str(value)
        return str(value)  # 其他情况转为字符串
    
    expected_default = normalize_default(expected_default_raw)
    actual_default = normalize_default(actual_default_raw)
    
    # 比较标准化后的默认值
    if expected_default != actual_default:
        # 特殊处理：如果预期是 None 而实际是空字符串 '' (或反之)，可能也视为匹配
        if not ((expected_default is None and actual_default == '') or \
                (expected_default == '' and actual_default is None)):
            is_correct = False
            logger.error(f"❌ 表 {table_name} 中字段 {field_name} 的默认值不匹配: 预期 '{expected_default_raw}' (标准化为 '{expected_default}'), 实际 '{actual_default_raw}' (标准化为 '{actual_default}')")
    
    # 4. 检查主键/索引 (Key)
    expected_key = expected_col.get('Key', '').upper()  # 标准化为大写
    actual_key = actual_col.get('Key', '').upper()      # 标准化为大写
    if expected_key and expected_key != actual_key:  # 仅当预期有 Key 时才比较
        is_correct = False
        logger.error(f"❌ 表 {table_name} 中字段 {field_name} 的键类型(Key)不匹配: 预期 '{expected_key}', 实际 '{actual_key}'")
    
    # 5. 检查额外属性 (Extra)
    expected_extra = expected_col.get('Extra', '').lower()  # 标准化为小写
    actual_extra = actual_col.get('Extra', '').lower()      # 标准化为小写
    if expected_extra and expected_extra != actual_extra:  # 仅当预期有 Extra 时才比较
        # 特殊处理 auto_increment vs AUTO_INCREMENT
        if 'auto_increment' in expected_extra and 'auto_increment' in actual_extra:
            pass  # 如果都包含 auto_increment 则视为匹配
        else:
            is_correct = False
            logger.error(f"❌ 表 {table_name} 中字段 {field_name} 的额外属性(Extra)不匹配: 预期 '{expected_extra}', 实际 '{actual_extra}'")
    
    return is_correct

def verify_table_columns(cursor, db_name, table_name, expected_structure):
    """验证表的字段结构"""
    cursor.execute(f"DESCRIBE {table_name}")
    actual_columns = cursor.fetchall()
    
    # 使用导入的 expected_structure
    expected_columns = expected_structure[db_name][table_name]
    
    structure_correct = True
    missing_fields = []
    
    for expected_col in expected_columns:
        field_name = expected_col['Field']
        matching_cols = [col for col in actual_columns if col['Field'] == field_name]
        
        if not matching_cols:
            structure_correct = False
            logger.error(f"❌ 表 {table_name} 缺少字段: {field_name}") # 保留原始日志
            missing_fields.append(expected_col)
            continue
        
        field_correct = verify_field(matching_cols[0], expected_col, table_name)
        structure_correct = structure_correct and field_correct
    
    if missing_fields and auto_update_table_structure():
        try:
            all_fields_added = True
            for field in missing_fields:
                success = add_missing_field(cursor, table_name, field)
                all_fields_added = all_fields_added and success
            
            if all_fields_added:
                cursor.connection.commit()
                logger.warning(f" 表 {table_name} 结构已自动更新")
                structure_correct = True 
            else:
                cursor.connection.rollback()
                logger.error(f"❌ 无法添加所有缺失的字段到表 {table_name}") # 保留原始日志
        except Exception as e:
            cursor.connection.rollback()
            logger.error(f"❌ 自动更新表结构失败: {str(e)}") # 保留原始日志
    
    return structure_correct

def verify_table_indexes(cursor, db_name, table_name):
    """验证表的索引结构"""
    cursor.execute(f"SHOW INDEXES FROM {table_name}")
    actual_indexes = cursor.fetchall()
    
    # 使用导入的 EXPECTED_INDEXES
    if db_name not in EXPECTED_INDEXES or table_name not in EXPECTED_INDEXES[db_name]:
        return True
    
    expected_indexes = EXPECTED_INDEXES[db_name][table_name]
    
    actual_index_groups = {}
    actual_columns_indexed = {}
    
    for idx in actual_indexes:
        key_name = idx['Key_name']
        column_name = idx['Column_name']
        
        if key_name not in actual_index_groups:
            actual_index_groups[key_name] = []
        actual_index_groups[key_name].append(idx)
        
        if column_name not in actual_columns_indexed:
            actual_columns_indexed[column_name] = []
        actual_columns_indexed[column_name].append(key_name)
    
    index_correct = True
    for expected_idx in expected_indexes:
        key_name = expected_idx['Key_name']
        column_name = expected_idx['Column_name']
        
        if key_name in actual_index_groups:
            column_in_index = any(idx['Column_name'] == column_name for idx in actual_index_groups[key_name])
            
            if not column_in_index:
                logger.warning(f"⚠️ 表 {table_name} 中索引 {key_name} 存在，但不包含列 {column_name}")
        else:
            if column_name not in actual_columns_indexed:
                index_correct = False
                logger.error(f"❌ 表 {table_name} 中列 {column_name} 没有被任何索引覆盖，预期索引: {key_name}")
    
    return index_correct

def verify_no_extra_fields(cursor, db_name, table_name, expected_structure):
    """验证表中没有额外的字段"""
    cursor.execute(f"DESCRIBE {table_name}")
    actual_columns = cursor.fetchall()
    
    # 使用导入的 expected_structure
    expected_columns = expected_structure[db_name][table_name]
    expected_field_names = [col['Field'] for col in expected_columns]
    
    extra_fields = []
    for actual_col in actual_columns:
        if actual_col['Field'] not in expected_field_names:
            extra_fields.append(actual_col['Field'])
    
    if extra_fields:
        logger.warning(f"⚠️ 表 {table_name} 存在额外的字段: {', '.join(extra_fields)}")
    
    return True

def verify_table(cursor, db_name, table_name, expected_structure):
    """验证表结构"""
    logger.warning(f"🔍 正在验证表 {table_name} 的结构...")
    
    # 1. 验证字段结构
    columns_verified = verify_table_columns(cursor, db_name, table_name, expected_structure)
    
    if not columns_verified and auto_update_table_structure():
        columns_verified = verify_table_columns(cursor, db_name, table_name, expected_structure)

    if not columns_verified:
        return False
    
    # 2. 验证索引和外键
    index_verified = verify_table_indexes(cursor, db_name, table_name)
    if not index_verified:
        return False
    
    # 3. 检查是否有额外的字段
    verify_no_extra_fields(cursor, db_name, table_name, expected_structure)
    
    logger.info(f" 表 {table_name} 验证通过 (结构和索引)") # 保留原始日志
    return True

def verify_db_structure(db_name, expected_structure, create_tables_dict):
    """验证数据库表结构，如果不存在则创建"""
    logger.info(f"========= 开始验证 {db_name} 数据库的表结构 ========= ")
    
    # 使用导入的 get_db_config
    db_config = get_db_config(db_name) 
    if not db_config:
        logger.error(f"❌ {db_name} 的数据库配置未能从环境变量加载") # 保留原始日志
        return False

    # 使用导入的 create_db_connection
    connection = create_db_connection(db_config)
    if not connection:
        return False

    try:
        with connection.cursor() as cursor:
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
            cursor.execute(f"USE {db_name}")
            logger.info(f" 已选择数据库: {db_name}") # 保留原始日志
            
            cursor.execute("SHOW TABLES")
            existing_tables = [table[f'Tables_in_{db_name.lower()}'] for table in cursor.fetchall()]
            logger.info(f"📊 数据库 {db_name} 包含以下表: {', '.join(existing_tables) if existing_tables else '无'}") # 保留原始日志
            
            all_tables_verified = True
            tables_updated = False
            
            for table_name, create_sql in create_tables_dict.items():
                if table_name not in existing_tables:
                    all_tables_verified = create_table(cursor, connection, table_name, create_sql) and all_tables_verified
                else:
                    before_verify = all_tables_verified
                    # 使用导入的 expected_structure
                    all_tables_verified = verify_table(cursor, db_name, table_name, expected_structure) and all_tables_verified
                    
                    if not before_verify and all_tables_verified:
                        tables_updated = True
            
            if all_tables_verified:
                if tables_updated:
                    logger.warning(f" {db_name} 数据库的表结构经过自动更新后验证完成")
                else:
                    logger.info(f" {db_name} 数据库的所有表结构验证完成") # 保留原始日志
                return True
            else:
                if auto_update_table_structure():
                    logger.error(f"❌ {db_name} 数据库的表结构验证失败，自动更新未能修复所有问题") # 保留原始日志
                else:
                    logger.error(f"❌ {db_name} 数据库的表结构验证失败，自动更新功能未启用") # 保留原始日志
                return False
            
    except Exception as e:
        logger.error(f"❌ 验证数据库结构时发生错误: {str(e)}") # 保留原始日志
        return False
    finally:
        if connection and connection.open: 
            connection.close()
            logger.info("🔄 数据库连接已关闭") # 保留原始日志

def validate_db_config():
    """验证环境变量中的数据库配置是否完整且有效"""
    # 使用导入的 MAILBOX_DB_CONFIG 和 PRESS_BUTTON_DB_CONFIG
    logger.info("🔍 开始验证数据库配置...") # 保留原始日志
    required_keys = ['host', 'user', 'password', 'database', 'charset']
    
    configs_to_validate = [
        {'config': MAILBOX_DB_CONFIG, 'name': '匿名留言小程序'},
        {'config': PRESS_BUTTON_DB_CONFIG, 'name': '按钮小程序'}
    ]
    
    config_valid = True
    for config_data in configs_to_validate:
        config = config_data['config']
        name = config_data['name']
        
        if not config or not isinstance(config, dict):
            logger.error(f"❌ {name}数据库配置未能从环境变量正确加载或解析") # 保留原始日志
            config_valid = False
            continue
            
        for key in required_keys:
            if key not in config:
                logger.error(f"❌ {name}数据库配置缺少必要字段: {key}") # 保留原始日志
                config_valid = False

    if config_valid:
        logger.info(" 数据库配置验证通过") # 保留原始日志
    else:
        logger.error("❌ 数据库配置验证失败") # 保留原始日志

    return config_valid

def initialize_database():
    """初始化所有数据库结构"""
    logger.info("=== 开始初始化数据库结构 ===") # 保留原始日志
    
    # 使用导入的 MAILBOX_TABLES, PRESS_BUTTON_TABLES
    db_configs = [
        {'name': 'mailbox', 'label': '匿名留言小程序', 'tables': MAILBOX_TABLES},
        {'name': 'pressthebutton', 'label': '按钮小程序', 'tables': PRESS_BUTTON_TABLES}
    ]
    
    all_success = True
    for config in db_configs:
        # 使用导入的 EXPECTED_TABLE_STRUCTURE
        status = verify_db_structure(config['name'], EXPECTED_TABLE_STRUCTURE, config['tables'])
        logger.info(f"{'' if status else '❌'} {config['label']}数据库初始化状态: {'成功' if status else '失败'}") # 保留原始日志
        all_success = all_success and status
    
    if not all_success:
        logger.error("❌ 数据库初始化失败，请检查错误日志并修复问题") # 保留原始日志
        return False
    
    logger.info(" ✅ 所有数据库初始化成功") # 保留原始日志
    return True 