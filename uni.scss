@import '@/uni_modules/uni-scss/variables.scss';

/* 滚动条隐藏 - 来自 隐藏滚动条完全指南.md */
/* =============================== */

/* 定义隐藏滚动条的混合器 (Mixin) */
@mixin hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 全局隐藏 WebKit 滚动条 */
::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

/* 对常见滚动容器应用混合器 (使用非嵌套写法) */
page,
scroll-view {
  @include hide-scrollbar;
}

/* 显式隐藏常见滚动容器的 WebKit 滚动条 (使用非嵌套写法) */
page::-webkit-scrollbar,
scroll-view::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* 如果你的项目中有其他常用的滚动容器类名，也可以在这里添加，例如：*/
/*
.my-custom-scroll-container {
  @include hide-scrollbar;
}
.my-custom-scroll-container::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}
*/

/* =============================== */