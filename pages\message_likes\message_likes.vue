<template>
	<view class="likes-container">
		<!-- 头部区域 -->
		<view class="header">
			<view class="decorative-blob blob-1"></view>
			<view class="decorative-blob blob-2"></view>
			<view class="header-content">
				<text class="title">点赞记录</text>
				<text class="subtitle">查看你的点赞互动</text>
			</view>
		</view>

		<!-- 分类标签栏 -->
		<view class="tab-bar">
			<view
				class="tab-item"
				:class="{ active: activeTab === 'received' }"
				@tap="switchTab('received')"
			>
				<view class="tab-icon">
					<view class="icon-heart received-heart"></view>
				</view>
				<text class="tab-text">被点赞</text>
				<view class="tab-count" v-if="receivedCount > 0">{{ receivedCount }}</view>
			</view>

			<view
				class="tab-item"
				:class="{ active: activeTab === 'given' }"
				@tap="switchTab('given')"
			>
				<view class="tab-icon">
					<view class="icon-heart given-heart"></view>
				</view>
				<text class="tab-text">我点赞</text>
				<view class="tab-count" v-if="givenCount > 0">{{ givenCount }}</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<view class="content">
			<!-- 被点赞列表 -->
			<view v-if="activeTab === 'received'" class="likes-list">
				<view v-if="receivedLikes.length === 0" class="empty-state">
					<view class="empty-icon">
						<image src="/static/empty-heart.png" mode="aspectFit" class="empty-image"></image>
					</view>
					<text class="empty-text">还没有人给你点赞</text>
					<text class="empty-subtext">写更多精彩的留言来获得点赞吧~</text>
				</view>

				<view v-else>
					<view
						v-for="(item, index) in receivedLikes"
						:key="index"
						class="like-item"
						@tap="viewMessage(item.message_id)"
					>
						<view class="like-avatar">
							<image :src="formatAvatarUrl(item.liker_avatar)" mode="aspectFill" class="avatar-image"></image>
						</view>
						<view class="like-content">
							<view class="like-header">
								<text class="liker-name">{{ item.liker_name || '匿名用户' }}</text>
								<text class="like-time">{{ formatTime(item.created_at) }}</text>
							</view>
							<view class="message-preview">
								<text class="message-text">{{ item.message_content }}</text>
							</view>
						</view>
						<view class="like-indicator">
							<view class="heart-icon liked"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 我点赞列表 -->
			<view v-if="activeTab === 'given'" class="likes-list">
				<view v-if="givenLikes.length === 0" class="empty-state">
					<view class="empty-icon">
						<image src="/static/empty-heart.png" mode="aspectFit" class="empty-image"></image>
					</view>
					<text class="empty-text">你还没有点过赞</text>
					<text class="empty-subtext">去发现更多精彩的留言吧~</text>
				</view>

				<view v-else>
					<view
						v-for="(item, index) in givenLikes"
						:key="index"
						class="like-item"
						@tap="viewMessage(item.message_id)"
					>
						<view class="like-avatar">
							<image :src="formatAvatarUrl(item.author_avatar)" mode="aspectFill" class="avatar-image"></image>
						</view>
						<view class="like-content">
							<view class="like-header">
								<text class="liker-name">{{ item.author_name || '匿名作者' }}</text>
								<text class="like-time">{{ formatTime(item.created_at) }}</text>
							</view>
							<view class="message-preview">
								<text class="message-text">{{ item.message_content }}</text>
							</view>
						</view>
						<view class="like-indicator">
							<view class="heart-icon liked"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-state">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				activeTab: 'received', // 'received' 或 'given'
				receivedLikes: [], // 被点赞列表
				givenLikes: [], // 我点赞列表
				receivedCount: 0,
				givenCount: 0,
				isLoading: false,
				token: null
			}
		},
		onLoad() {
			this.token = uni.getStorageSync('mailbox_token');
			this.loadLikesData();
		},
		methods: {
			// 切换标签
			switchTab(tab) {
				this.activeTab = tab;
				this.loadLikesData();
			},

			// 加载点赞数据
			async loadLikesData() {
				if (!this.token) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					return;
				}

				this.isLoading = true;

				try {
					const endpoint = this.activeTab === 'received' ? 'received_likes' : 'given_likes';
					const res = await uni.request({
						url: `${this.$baseUrl}/mailbox/likes/${endpoint}`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${this.token}`
						}
					});

					if (res.statusCode === 200 && res.data) {
						if (this.activeTab === 'received') {
							this.receivedLikes = res.data.likes || [];
							this.receivedCount = res.data.total || 0;
						} else {
							this.givenLikes = res.data.likes || [];
							this.givenCount = res.data.total || 0;
						}
					} else if (res.statusCode === 401) {
						uni.showToast({
							title: '登录已过期，请重新登录',
							icon: 'none'
						});
					} else {
						console.error('获取点赞数据失败:', res);
					}
				} catch (error) {
					console.error('请求点赞数据失败:', error);
					uni.showToast({
						title: '网络请求失败',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 查看留言详情
			viewMessage(messageId) {
				if (!messageId) return;

				uni.navigateTo({
					url: `/pages/message_detail/message_detail?id=${messageId}`
				});
			},

			// 格式化头像URL
			formatAvatarUrl(url) {
				if (!url) {
					return '/static/default-avatar.png';
				}
				if (url.startsWith('http')) {
					return url;
				}
				const baseUrl = this.$baseUrl.endsWith('/') ? this.$baseUrl.slice(0, -1) : this.$baseUrl;
				const avatarPath = url.startsWith('/') ? url : `/${url}`;
				return baseUrl + avatarPath;
			},

			// 格式化时间
			formatTime(timestamp) {
				if (!timestamp) return '';

				const now = new Date();
				const time = new Date(timestamp);
				const diff = now - time;

				const minute = 60 * 1000;
				const hour = 60 * minute;
				const day = 24 * hour;

				if (diff < minute) {
					return '刚刚';
				} else if (diff < hour) {
					return `${Math.floor(diff / minute)}分钟前`;
				} else if (diff < day) {
					return `${Math.floor(diff / hour)}小时前`;
				} else if (diff < 7 * day) {
					return `${Math.floor(diff / day)}天前`;
				} else {
					return time.toLocaleDateString();
				}
			}
		}
	}
</script>

<style lang="scss">
/* 基础容器样式 */
.likes-container {
	min-height: 100vh;
	background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
	position: relative;
	overflow: hidden;
}

/* 头部区域 */
.header {
	height: 300rpx;
	position: relative;
	overflow: hidden;
	padding: 60rpx 40rpx 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 装饰背景 */
.decorative-blob {
	position: absolute;
	border-radius: 50%;
	filter: blur(40rpx);
	opacity: 0.6;
}

.blob-1 {
	width: 300rpx;
	height: 300rpx;
	background: linear-gradient(135deg, rgba(255, 107, 129, 0.4), rgba(255, 158, 158, 0.3));
	top: -100rpx;
	left: -50rpx;
	animation: float 8s ease-in-out infinite;
}

.blob-2 {
	width: 250rpx;
	height: 250rpx;
	background: linear-gradient(135deg, rgba(118, 108, 219, 0.4), rgba(165, 199, 254, 0.3));
	top: -80rpx;
	right: -80rpx;
	animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
	0%, 100% {
		transform: translateY(0) rotate(0deg);
	}
	50% {
		transform: translateY(-20rpx) rotate(180deg);
	}
}

.header-content {
	text-align: center;
	position: relative;
	z-index: 2;
}

.title {
	font-size: 48rpx;
	font-weight: 700;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
	font-size: 28rpx;
	color: #666;
	opacity: 0.8;
}

/* 标签栏 */
.tab-bar {
	display: flex;
	background: #fff;
	margin: 0 30rpx;
	border-radius: 20rpx;
	padding: 10rpx;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 10;
	margin-top: -60rpx;
}

.tab-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 25rpx 20rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
	position: relative;
	cursor: pointer;
}

.tab-item.active {
	background: linear-gradient(135deg, rgba(255, 107, 129, 0.1), rgba(255, 158, 158, 0.05));
	transform: translateY(-2rpx);
}

.tab-icon {
	margin-bottom: 8rpx;
	position: relative;
}

.icon-heart {
	width: 40rpx;
	height: 40rpx;
	position: relative;
}

.icon-heart::before {
	content: '';
	position: absolute;
	width: 40rpx;
	height: 40rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
}

.received-heart::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
}

.given-heart::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23766cdb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
}

.tab-item.active .received-heart::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ff6b81' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
}

.tab-item.active .given-heart::before {
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23766cdb' stroke='%23766cdb' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
}

.tab-text {
	font-size: 26rpx;
	color: #666;
	font-weight: 500;
	transition: color 0.3s ease;
}

.tab-item.active .tab-text {
	color: #333;
	font-weight: 600;
}

.tab-count {
	position: absolute;
	top: 8rpx;
	right: 15rpx;
	background: #ff6b81;
	color: #fff;
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 10rpx;
	min-width: 20rpx;
	text-align: center;
	line-height: 1;
}

/* 内容区域 */
.content {
	padding: 40rpx 30rpx;
	min-height: calc(100vh - 400rpx);
}

/* 点赞列表 */
.likes-list {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.05);
}

.like-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f5f5f5;
	transition: background-color 0.3s ease;
	cursor: pointer;
}

.like-item:last-child {
	border-bottom: none;
}

.like-item:active {
	background-color: rgba(0, 0, 0, 0.02);
}

.like-avatar {
	margin-right: 20rpx;
}

.avatar-image {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.like-content {
	flex: 1;
	min-width: 0;
}

.like-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8rpx;
}

.liker-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}

.like-time {
	font-size: 22rpx;
	color: #999;
}

.message-preview {
	background: rgba(0, 0, 0, 0.02);
	padding: 15rpx;
	border-radius: 10rpx;
	border-left: 4rpx solid #ff6b81;
}

.message-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
}

.like-indicator {
	margin-left: 20rpx;
}

.heart-icon {
	width: 30rpx;
	height: 30rpx;
	position: relative;
}

.heart-icon.liked::before {
	content: '';
	position: absolute;
	width: 30rpx;
	height: 30rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='%23ff6b81' stroke='%23ff6b81' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'%3E%3C/path%3E%3C/svg%3E");
	background-size: cover;
	background-repeat: no-repeat;
	animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 100rpx 40rpx;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.empty-image {
	width: 120rpx;
	height: 120rpx;
	opacity: 0.3;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	font-weight: 500;
	display: block;
	margin-bottom: 15rpx;
}

.empty-subtext {
	font-size: 26rpx;
	color: #999;
	line-height: 1.4;
}

/* 加载状态 */
.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 60rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #ff6b81;
	border-radius: 50%;
	animation: spin 1s linear infinite;
	margin-bottom: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 26rpx;
	color: #666;
}
</style>
