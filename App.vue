<script>
	// 新增: 定义缓存键名
	const PROFILE_CACHE_KEY = 'user_profile_cache';
	// 新增: 定义缓存过期时间（毫秒）- 3天
	const CACHE_EXPIRATION = 3 * 24 * 60 * 60 * 1000; // 3天
	
	export default {
		globalData: {
			token: null,
			userInfo: {
				nickname: '',
				avatarUrl: '',
				is_banned: false,
				remark: ''
			}
		},
		onLaunch: function() {
			console.warn('当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！')
			console.log('App Launch')
			// 移除这里的登录调用，避免重复
		},
		onShow: function() {
			console.log('App Show')
			uni.showShareMenu({
			  withShareTicket: true,
			  showShareItems: ['qq', 'qzone', 'wechatFriends', 'wechatMoment']
			});
			// 只在应用显示时检查登录状态
			this.ensureLogin()
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 确保用户已登录
			async ensureLogin() {
				try {
					// 检查本地是否有token
					const savedToken = uni.getStorageSync('mailbox_token')
					
					if (savedToken) {
						// 验证token是否有效
						const isValid = await this.verifyToken(savedToken)
						if (isValid) {
							this.globalData.token = savedToken
							// 获取用户信息
							await this.fetchUserInfo()
							console.log('用户已登录，token有效')
							return
						}
					}
					
					// 没有token或token无效，执行登录
					await this.performLogin()
				} catch (error) {
					console.error('登录失败:', error)
				}
			},
			
			// 验证token有效性
			verifyToken(token) {
				const base = this.$baseUrl || (typeof getApp === 'function' && getApp().$vm ? getApp().$vm.$baseUrl : '');
				return new Promise((resolve) => {
					uni.request({
						url: `${base}/mailbox/profile`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${token}`
						},
						success: (res) => {
							resolve(res.statusCode === 200)
						},
						fail: () => {
							resolve(false)
						}
					})
				})
			},
			
			// 执行登录
			performLogin() {
				return new Promise((resolve, reject) => {
					uni.login({
						success: (loginRes) => {
							if (loginRes.code) {
								this.requestToken(loginRes.code, resolve, reject)
							} else {
								reject(new Error('获取登录code失败'))
							}
						},
						fail: (err) => {
							reject(err)
						}
					})
				})
			},
			
			// 请求token
			requestToken(code, resolve, reject) {
				const platform = this.getPlatform();
				const base = this.$baseUrl || (typeof getApp === 'function' && getApp().$vm ? getApp().$vm.$baseUrl : '');
				uni.request({
					url: `${base}/mailbox/get_openid`,
					method: 'POST',
					data: {
						code,
						platform
					},
					success: async (response) => {
						if (response.data.token) {
							this.globalData.token = response.data.token
							uni.setStorageSync('mailbox_token', response.data.token)
							// 登录成功后获取用户信息
							await this.fetchUserInfo()
							console.log('登录成功，token已保存')
							resolve()
						} else {
							reject(new Error('未能获取token'))
						}
					},
					fail: (err) => {
						reject(err)
					}
				})
			},
			
			// 获取用户信息 - 增强版，包括缓存功能
			async fetchUserInfo() {
				if (!this.globalData.token) return
				
				const base = this.$baseUrl || (typeof getApp === 'function' && getApp().$vm ? getApp().$vm.$baseUrl : '');
				
				try {
					// 检查缓存是否存在且未过期
					const cachedProfile = uni.getStorageSync(PROFILE_CACHE_KEY);
					const cachedTime = uni.getStorageSync(PROFILE_CACHE_KEY + '_time');
					const now = Date.now();
					
					// 如果缓存有效且未过期，直接使用缓存数据
					if (cachedProfile && cachedProfile.nickname && 
						cachedTime && (now - cachedTime < CACHE_EXPIRATION)) {
						this.globalData.userInfo = cachedProfile;
						console.log('从缓存加载用户信息:', this.globalData.userInfo);
						return;
					}
					
					// 缓存不存在或已过期，从服务器获取
					console.log('从服务器获取用户信息');
					const res = await uni.request({
						url: `${base}/mailbox/profile`,
						method: 'GET',
						header: {
							'Authorization': `Bearer ${this.globalData.token}`
						}
					})
					
					if (res.statusCode === 200 && res.data) {
						// 更新全局用户信息
						const profileData = {
							nickname: res.data.nickname || '',
							avatarUrl: res.data.avatarUrl || '',
							is_banned: res.data.is_banned || false,
							remark: res.data.remark || ''
						};
						
						this.globalData.userInfo = profileData;
						
						// 缓存用户信息和缓存时间
						uni.setStorageSync(PROFILE_CACHE_KEY, profileData);
						uni.setStorageSync(PROFILE_CACHE_KEY + '_time', now);
						
						if(this.globalData.userInfo.is_banned) {
							console.warn('当前用户已被禁用，原因:', this.globalData.userInfo.remark || '未提供');
						}
						
						console.log('用户信息获取成功并已缓存:', this.globalData.userInfo);
					} else {
						console.error('获取用户信息失败:', res);
						
						// 如果API请求失败，尝试使用可能存在的缓存数据（即使已过期）
						const oldCachedProfile = uni.getStorageSync(PROFILE_CACHE_KEY);
						if (oldCachedProfile && oldCachedProfile.nickname) {
							this.globalData.userInfo = oldCachedProfile;
							console.log('API获取用户信息失败，使用过期缓存数据:', this.globalData.userInfo);
						}
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
					
					// 网络错误时，尝试使用缓存（即使已过期）
					const oldCachedProfile = uni.getStorageSync(PROFILE_CACHE_KEY);
					if (oldCachedProfile && oldCachedProfile.nickname) {
						this.globalData.userInfo = oldCachedProfile;
						console.log('网络请求用户信息失败，使用缓存数据:', this.globalData.userInfo);
					}
				}
			},
			
			// 新增: 手动刷新用户信息的方法
			async refreshUserInfo() {
				// 删除缓存的时间戳，强制下次重新获取
				uni.removeStorageSync(PROFILE_CACHE_KEY + '_time');
				return this.fetchUserInfo(); // 重新获取
			},
			
			// 获取平台信息
			getPlatform() {
				// #ifdef MP-WEIXIN
				return 'WX'
				// #endif
				// #ifdef MP-QQ
				return 'QQ'
				// #endif
				return 'WX' // 默认微信
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	// 设置整个项目的背景色
	page {
		background-color: #f5f5f5;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}
</style>