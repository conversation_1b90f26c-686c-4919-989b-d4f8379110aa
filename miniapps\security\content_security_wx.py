import requests
import time
import threading
import logging
import json # 导入json模块

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

class ContentSecurityWX:
    def __init__(self, appid, secret):
        self.appid = appid
        self.secret = secret
        self.access_token = None
        self.expires_at = 0
        self.lock = threading.Lock()

    def get_access_token(self):
        """获取微信接口调用凭证access_token (使用稳定版接口)"""
        with self.lock:
            if time.time() >= self.expires_at:
                # 使用稳定版 token 接口
                url = "https://api.weixin.qq.com/cgi-bin/stable_token"
                payload = {
                    "grant_type": "client_credential",
                    "appid": self.appid,
                    "secret": self.secret
                }
                headers = {'Content-Type': 'application/json'}
                
                try:
                    # logging.debug(f"Attempting to get stable access_token with payload: {json.dumps(payload)}") # 可选的调试日志
                    response = requests.post(url, data=json.dumps(payload), headers=headers, timeout=5) # 增加超时
                    response.raise_for_status() # 如果HTTP状态码是4xx/5xx，则抛出异常
                    data = response.json()
                    # logging.debug(f"Received from stable_token API: {data}") # 可选的调试日志
                except requests.exceptions.RequestException as e:
                    logging.error(f"请求微信稳定版访问令牌网络错误: {e}")
                    raise Exception(f"请求微信稳定版访问令牌网络错误: {e}")
                except json.JSONDecodeError as e:
                    logging.error(f"解析微信稳定版访问令牌响应JSON错误: {e}. Response text: {response.text if 'response' in locals() else 'N/A'}")
                    raise Exception(f"解析微信稳定版访问令牌响应JSON错误: {e}")

                if 'access_token' in data and data.get('expires_in'):
                    self.access_token = data['access_token']
                    # 稳定版 token 通常有效期较长，expires_in 也可能更大
                    self.expires_at = time.time() + data['expires_in'] - 300  # 提前5分钟刷新
                    logging.info(f"成功获取并更新了微信稳定版访问令牌。下次刷新大约在: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.expires_at))}")
                else:
                    # 如果返回的数据中没有 access_token 或 expires_in，记录错误
                    errcode = data.get('errcode', 'N/A')
                    errmsg = data.get('errmsg', '未知错误')
                    logging.error(f"获取微信稳定版访问令牌失败: errcode={errcode}, errmsg='{errmsg}', data={data}")
                    # 保留原有异常，以便上层知道获取失败
                    raise Exception(f"获取微信稳定版访问令牌失败: errcode={errcode}, errmsg='{errmsg}'")
            return self.access_token

    def msg_sec_check(self, content, openid, scene=1, user_id=None, _retry_attempted=False):
        """
        检查内容是否安全 (微信平台)
        
        参数:
            content: 要检查的文本内容
            openid: 用户的OpenID
            scene: 场景值（1-资料，2-评论，3-论坛，4-社交日志）
            user_id: 用户ID（数据表中的id字段，不是openid）
            _retry_attempted: 内部标志，防止无限重试
            
        返回:
            bool: 内容是否安全
        """
        try:
            # 获取访问令牌
            access_token = self.get_access_token()
            url = f"https://api.weixin.qq.com/wxa/msg_sec_check?access_token={access_token}"
            
            data = {
                "openid": openid,
                "scene": scene,
                "version": 2,
                "content": content
            }
            
            # 手动编码请求体，确保UTF-8且中文不转义
            headers = {'Content-Type': 'application/json;charset=utf-8'}
            try:
                encoded_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
                # 添加请求体日志 (DEBUG级别)
                logging.debug(f"[内容安全检查] 平台:WX - Request Body (Bytes): {encoded_data}")
            except Exception as encode_err:
                 logging.error(f"[内容安全检查] 平台:WX - Failed to encode request data: {encode_err}")
                 # Encoding errors are critical, re-raise or return False. Let's return False for safety.
                 return False
            
            # 发送请求 (使用 data 参数传递字节流)
            response = requests.post(url, data=encoded_data, headers=headers)
            
            # 添加响应日志 (DEBUG级别)
            result = None
            try:
                # 记录原始响应内容
                raw_response_content = response.content.decode('utf-8', errors='ignore') # 尝试解码为 utf-8, 忽略错误
                logging.debug(f"[内容安全检查] 平台:WX - Raw Response: {raw_response_content}")
                # 解析 JSON
                result = response.json()
                logging.debug(f"[内容安全检查] 平台:WX - Parsed Response: {result}")
            except json.JSONDecodeError:
                 logging.error(f"[内容安全检查] 平台:WX - Failed to decode JSON response. Status Code: {response.status_code}. Raw content: {response.content}")
                 result = {"errcode": -999, "errmsg": "JSON Decode Error"} # 自定义错误码
            except Exception as decode_err:
                 logging.error(f"[内容安全检查] 平台:WX - Error processing response: {decode_err}. Raw content: {response.content}")
                 result = {"errcode": -998, "errmsg": "Response Processing Error"} # 自定义错误码
            
            errcode = result.get('errcode')

            # --- 重试逻辑开始 ---
            if errcode == 40001: # access_token 无效
                if not _retry_attempted:
                    logging.warning(f"[内容安全检查] 平台:WX - Access token invalid (errcode 40001) for user {user_id}. Forcing refresh and retrying once.")
                    with self.lock:
                        self.expires_at = 0  # 强制下次调用 get_access_token 时刷新
                    return self.msg_sec_check(content, openid, scene, user_id, _retry_attempted=True)
                else:
                    logging.error(f"[内容安全检查] 平台:WX - Access token still invalid (errcode 40001) for user {user_id} after retry.")
                    return False # 重试后仍然失败
            # --- 重试逻辑结束 ---
            
            if errcode != 0:
                # API调用失败 (非40001，或40001重试后仍失败的点已在上面处理)
                status = "调用失败"
                logging.error(f"[内容安全检查] 平台:WX 用户ID:{user_id} 结果:{status} 错误:{result} 内容:{content}")
                return False
            
            # 解析检查结果
            check_result = result.get('result', {})
            suggest = check_result.get('suggest', 'pass')
            
            # 检查结果
            is_safe = suggest != 'risky'
            status = "通过" if is_safe else "不通过"
            
            # 统一日志格式
            log_message = f"[内容安全检查] 平台:WX 用户ID:{user_id} 结果:{status} 内容:{content}"
            
            if is_safe:
                logging.info(log_message)
            else:
                logging.warning(log_message)
            
            return is_safe
            
        except Exception as e:
            # 捕获 get_access_token() 失败、requests.post() 网络错误等
            if not _retry_attempted and \
               ("获取微信访问令牌失败" in str(e) or isinstance(e, requests.exceptions.RequestException) or "Failed to encode request data" in str(e)):
                # 如果是初次尝试时获取token失败、网络请求失败或编码失败，则重试一次
                logging.warning(
                    f"[内容安全检查] 平台:WX - Error during security check for user {user_id} (will retry once): {str(e)}. Content: {content}"
                )
                with self.lock:
                    self.expires_at = 0  # 强制 token 刷新
                time.sleep(0.1)  # 短暂暂停后重试
                return self.msg_sec_check(content, openid, scene, user_id, _retry_attempted=True)

            # 如果已经重试过，或者是不适合重试的其他类型的异常
            logging.error(f"[内容安全检查] 平台:微信 用户ID:{user_id} 异常 (no retry or retry failed): {str(e)} 内容:{content}")
            return False # 发生无法处理的异常时，默认为不安全