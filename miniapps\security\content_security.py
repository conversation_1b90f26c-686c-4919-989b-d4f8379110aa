import requests
import time
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

class ContentSecurity:
    def __init__(self, appid, secret):
        self.appid = appid
        self.secret = secret
        self.access_token = None
        self.expires_at = 0
        self.lock = threading.Lock()

    def get_access_token(self):
        with self.lock:
            if time.time() >= self.expires_at:
                url = f"https://api.q.qq.com/api/getToken?grant_type=client_credential&appid={self.appid}&secret={self.secret}"
                response = requests.get(url)
                data = response.json()
                if 'access_token' in data:
                    self.access_token = data['access_token']
                    self.expires_at = time.time() + data['expires_in'] - 300  # 提前5分钟刷新
                else:
                    logging.error(f"获取访问令牌失败: {data}")
                    raise Exception(f"获取访问令牌失败: {data}")
            return self.access_token

    def msg_sec_check(self, content, user_id=None):
        """
        检查内容是否安全
        :param content: 要检查的内容
        :param user_id: 用户ID（数据表中的id字段，不是openid）
        :return: bool 内容是否安全
        """
        try:
            # 获取访问令牌
            access_token = self.get_access_token()
            url = f"https://api.q.qq.com/api/json/security/MsgSecCheck?access_token={access_token}"
            
            data = {
                "appid": self.appid,
                "content": content
            }
            
            # 发送请求
            response = requests.post(url, json=data)
            result = response.json()
            
            # 检查结果
            is_safe = result.get('errCode', -1) == 0
            status = "通过" if is_safe else "不通过"
            
            # 统一日志格式
            log_message = f"[内容安全检查] 平台:QQ 用户ID:{user_id} 结果:{status} 内容:{content}"
            
            if is_safe:
                logging.info(log_message)
            else:
                logging.warning(log_message)
                
            return is_safe
            
        except Exception as e:
            # 异常情况记录
            logging.error(f"[内容安全检查] 平台:QQ 用户ID:{user_id} 异常:{str(e)} 内容:{content}")
            raise e



'''

# 内容安全检查
    try:
        if not content_security.msg_sec_check(content):
            # logger.warning("评论内容包含不当内容")
            return jsonify({'success': False, 'error': 'Comment contains inappropriate content'}), 400
    except Exception as e:
        # logger.error(f"内容安全检查失败: {str(e)}")
        return jsonify({'success': False, 'error': 'Content security check failed'}), 500


# cmd测试命令：
curl -X POST http://127.0.0.1:5000/...... ^
     -H "Content-Type: application/json" ^
     -d "{\"scenarioId\": 1, \"content\": \"特3456书yuuo莞6543李zxcz蒜7782法fgnv级\", \"userId\": 1}"
'''