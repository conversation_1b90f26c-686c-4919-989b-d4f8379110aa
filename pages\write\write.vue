<!-- write.vue -->
<template>
  <view class="container">
    <!-- 背景效果元素 -->
    <view class="gradient-blob blob-1"></view>
    <view class="gradient-blob blob-2"></view>
    <view class="gradient-blob blob-3"></view>
    <view class="decorative-pattern"></view>
    
    <view class="content">
      <!-- 页面标题 -->
      <view class="header">
        <text class="title">写留言</text>
        <text class="subtitle">我们终将以陌生人的身份，交换最深的心事~</text>
      </view>

      <!-- 安全提示 -->
      <view class="safety-tip">
        <view class="safety-icon"></view>
        <text class="safety-tip-text">
          提示：为遵守相关法律法规，所有留言均会经过审核。禁止发布任何非法、骚扰、辱骂、诽谤、恐吓或侵犯他人隐私的内容。平台有权对违规留言进行追溯，并对违规用户进行封禁等处理。
        </text>
      </view>

      <!-- 表单区域 -->
      <view class="form-container" v-show="!showSuccessModal">
        <view class="form">
          <!-- 名字输入框 -->
          <view class="input-group">
            <view class="input-icon name-icon"></view>
            <input 
              class="input" 
              placeholder="输入留言对象名字" 
              v-model="name"
              @focus="onInputFocus('name')"
              @blur="onInputBlur('name')"
              :class="{ 'input-focus': focusedInput === 'name' }"
            />
            <view class="input-border" :class="{ 'input-border-active': focusedInput === 'name' }"></view>
          </view>
      
          <!-- 留言内容输入框 -->
          <view class="input-group">
            <view class="input-icon message-icon"></view>
            <textarea 
              class="textarea" 
              placeholder="输入留言" 
              v-model="content"
              @focus="onInputFocus('content')"
              @blur="onInputBlur('content')"
              :class="{ 'input-focus': focusedInput === 'content' }"
            />
            <view class="input-border" :class="{ 'input-border-active': focusedInput === 'content' }"></view>
          </view>
      
          <!-- 新增：签名卡片 -->
          <view class="signature-card">
              <view class="signature-left">
                  <!-- 头像 -->
                  <button v-if="$platform === 'WX'" 
                     class="avatar-button" 
                     :class="{'disabled': !showAuthor, 'animating': animating}"
                     open-type="chooseAvatar" 
                     @tap.stop="!showAuthor && showSignatureDisabledTip()"
                     @chooseavatar="onChooseAvatar">
                      <image class="avatar-image" 
                        :class="{'animating': animating}"
                        :src="showAuthor ? formatAvatarUrl(userInfo.avatarUrl) : '/static/default-avatar.png'" 
                        mode="aspectFill"></image>
                      <view class="avatar-edit-indicator" :class="{'hidden': !showAuthor}">
                          <view class="edit-icon"></view>
                      </view>
                  </button>
                  <button v-else 
                     class="avatar-button" 
                     :class="{'disabled': !showAuthor, 'animating': animating}"
                     open-type="getUserInfo" 
                     @tap.stop="!showAuthor && showSignatureDisabledTip()"
                     @getuserinfo="onGetUserInfo">
                      <image class="avatar-image" 
                        :class="{'animating': animating}"
                        :src="showAuthor ? formatAvatarUrl(userInfo.avatarUrl) : '/static/default-avatar.png'" 
                        mode="aspectFill"></image>
                  </button>
          
                  <!-- 昵称和提示 -->
                  <view class="nickname-section" 
                     :class="{'disabled': !showAuthor, 'animating': animating}"
                     @tap="showAuthor ? editNickname() : showSignatureDisabledTip()">
                      <block v-if="!isEditingNickname">
                          <text class="nickname-text" :class="{'animating': animating}">
                            {{ showAuthor ? (userInfo.nickname || '点击填写昵称') : '留言用户' }}
                          </text>
                          <text class="nickname-prompt" :class="{'animating': animating}">
                            {{ showAuthor ? '点击头像和昵称进行设置' : '开启署名后可修改' }}
                          </text>
                      </block>
                      <input v-else-if="$platform === 'WX'" type="nickname" class="nickname-input" :value="tempNickname" @input="onNicknameInput" @blur="onNicknameBlur" focus placeholder="请输入昵称" />
                      <input v-else type="text" class="nickname-input" :value="tempNickname" @input="onNicknameInput" @blur="onNicknameBlur" focus placeholder="请输入昵称" />
                  </view>
              </view>
              <view class="signature-right">
                 <!-- 临时：微信平台强制署名，后续可移除 disabled 属性中的平台判断 -->
                 <switch :checked="showAuthor" @change="onShowAuthorChange" color="#2a9d8f" style="transform: scale(0.8)" :disabled="animating || $platform === 'WX'"/>
                  <text class="switch-label">显示留言署名</text>
              </view>
          </view>
      
          <!-- 提交按钮 -->
          <button 
            class="submit-button animate-pulse" 
            @tap="submitMessage"
            :class="{ 'button-hover': isButtonHovered }"
            @touchstart="onButtonTouchStart"
            @touchend="onButtonTouchEnd"
          >
            <view class="button-icon"></view>
            <text class="button-text">提交留言</text>
            <view class="button-ripple" v-if="isButtonHovered"></view>
          </button>
        </view>
      </view>

      <!-- 提交成功弹窗 -->
      <view class="success-modal" v-if="showSuccessModal">
        <view class="modal-backdrop"></view>
        <view class="modal-content">
          <view class="success-icon animate-bounce"></view>
          <view class="modal-header">
            <text class="modal-title">留言已送出</text>
            <text class="modal-subtitle">你的悄悄话已经寄出啦~</text>
          </view>
          
          <view class="button-group">
            <button 
              class="action-button view-button animate-appear" 
              @tap="goToViewMessage"
            >
              <view class="view-icon"></view>
              <text class="button-text">查看刚写的留言</text>
            </button>
            
            <button 
              class="action-button share-button animate-appear" 
              style="animation-delay: 0.1s;"
              @tap="shareMessage"
              open-type="share"
            >
              <view class="share-icon"></view>
              <text class="button-text">分享给Ta</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'WritePage',

  data() {
    return {
      // 表单数据
      name: '',
      content: '',
      submittedName: '', // 新增：用于存储成功提交时的名字
      userInfo: {
        nickname: '',
        avatarUrl: ''
      },
      isEditingNickname: false,
      tempNickname: '',
      
      // UI状态
      focusedInput: '',
      isButtonHovered: false,
      showSuccessModal: false,
      
      token: null,
      

      
      // 分享配置
      shareConfig: {
        title: '有人给你写了一封悄悄话~', // 默认标题
        imageUrl: '/static/share-cover.jpg', // 默认图片
      },

      showAuthor: true, // 修改：默认显示作者信息
      animating: false, // 添加动画状态标记
      likeTemplateId: null, // 新增：用于存储点赞模板ID
    }
  },
  
  onLoad(options) {
    if (options.name) {
      this.name = decodeURIComponent(options.name);
    }
    
    // 尝试从本地存储获取token
    const savedToken = uni.getStorageSync('mailbox_token');
    if (savedToken) {
      this.token = savedToken;
    }
    
    // 从全局数据加载用户信息
    const app = getApp();
    if (app.globalData.userInfo) {
    	this.userInfo = JSON.parse(JSON.stringify(app.globalData.userInfo));
    }
    
    // 获取分享配置
    this.fetchShareConfig('write');
    // 新增：获取应用配置
    this.fetchAppConfig();
  },

  // 添加监听器
  watch: {
    showAuthor(newVal) {
      // 当显示作者状态改变时触发动画
      this.triggerAnimation();
    }
  },

  // 添加分享给好友的生命周期函数
  onShareAppMessage() {
    // --- 拼接完整的图片 URL ---
    let fullImageUrl = this.shareConfig.imageUrl;
    if (fullImageUrl && fullImageUrl.startsWith('/') && this.$baseUrl) {
      fullImageUrl = this.$baseUrl + fullImageUrl;
    }
    // --- URL 拼接结束 ---
    
    const shareInfo = {
      title: this.shareConfig.title,
      path: `/pages/view/view?name=${encodeURIComponent(this.name)}`,
      imageUrl: fullImageUrl, // 使用处理后的 URL
      success: () => {
        uni.showToast({
          title: '分享成功',
          icon: 'success',
          duration: 1500,
          complete: () => {
            setTimeout(() => {
              this.showSuccessModal = false;
              uni.navigateBack({
                url: '/pages/index/index',
                success: () => {
                },
                fail: (error) => {
                  console.error('返回首页失败:', error); // 保留错误日志
                }
              });
            }, 1500);
          }
        });
      },
      fail: (error) => {
        console.error('分享失败:', error); // 保留错误日志
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    };
    
    return shareInfo;
  },

  // 添加分享到朋友圈的生命周期函数
  onShareTimeline() {
    // --- 拼接完整的图片 URL ---
    let fullImageUrl = this.shareConfig.imageUrl;
    if (fullImageUrl && fullImageUrl.startsWith('/') && this.$baseUrl) {
      fullImageUrl = this.$baseUrl + fullImageUrl;
    }
    // --- URL 拼接结束 ---
    
    const shareInfo = {
      title: this.shareConfig.title,
      query: `name=${encodeURIComponent(this.name)}`,
      imageUrl: fullImageUrl // 使用处理后的 URL
    };
    return shareInfo;
  },

  methods: {
    // 关闭成功弹窗
    closeModal() {
      this.showSuccessModal = false;
    },

    async submitMessage() {
      if (this.showAuthor && (!this.userInfo.nickname || !this.userInfo.avatarUrl)) {
        uni.showToast({
          title: '请设置头像和昵称以署名',
          icon: 'none'
        });
        return;
      }

      if (this.showAuthor && this.userInfo.nickname === '没改名的微信用户') {
        uni.showToast({
          title: '请修改您的默认昵称',
          icon: 'none'
        });
        return;
      }

      // --- 统一的输入验证 ---
      // 1. 验证是否为空
      if (!this.name || this.name.trim() === '') {
        uni.showToast({ title: '请输入留言对象的名字', icon: 'none' });
        return;
      }
      if (!this.content || this.content.trim() === '') {
        uni.showToast({ title: '请输入留言内容', icon: 'none' });
        return;
      }

      // 2. 验证非法字符
      const invalidNameChars = /[%_ ]/; // 名字不允许 % _ 和空格
      const invalidContentChars = /[%_]/; // 内容不允许 % _

      if (this.name.includes('匿名') || invalidNameChars.test(this.name)) {
        uni.showToast({ title: '名字包含无效字符或空格', icon: 'none' });
        return;
      }
      if (this.content.includes('匿名') || invalidContentChars.test(this.content)) {
        uni.showToast({ title: '留言内容包含无效字符', icon: 'none' });
        return;
      }
      // --- 验证结束 ---
    
      uni.showLoading({
        title: '提交中...',
        mask: true
      })
    
      try {
        // 先确保获取到 OpenID/Token
        await this.getOpenId();

        // 定义实际发送消息的函数
        const sendRequestInternal = async () => {
          // 在发送请求前，保存当前的名字
          const currentNameForSubmission = this.name;

          const result = await this.sendMessageToServer();
        
          if (result.success) {
            this.submittedName = currentNameForSubmission; // 保存名字
            this.handleSubmitSuccess();
          } else if (result.code === 'token_expired' || result.code === 'auth_error') {
            // 如果token过期，尝试重新获取
            this.token = null;
            uni.removeStorageSync('mailbox_token');
          
            await this.getOpenId(); // 重新获取token
            const retryResult = await this.sendMessageToServer(); // 重试
          
            if (retryResult.success) {
              this.submittedName = currentNameForSubmission; // 保存名字
              this.handleSubmitSuccess();
            } else {
              this.handleSubmissionError(retryResult.error || retryResult);
            }
          } else {
            this.handleSubmissionError(result.error || result);
          }
        };

        // 判断是否为微信平台
        if (this.$platform === 'WX' && this.likeTemplateId) {
          console.log('当前为微信平台，尝试请求订阅消息 (点赞通知)');
          const tmplId = this.likeTemplateId; // 使用从后端获取的ID

          uni.requestSubscribeMessage({
            tmplIds: [tmplId],
            success: (res) => {
              console.log('请求订阅消息(点赞通知) API 调用成功:', res);
              if (res[tmplId] === 'accept') {
                console.log('用户同意订阅该模板消息');
              } else {
                console.log('用户拒绝或忽略了订阅请求(点赞通知):', res[tmplId]);
              }
            },
            fail: (err) => {
              console.error('请求订阅消息(点赞通知) API 调用失败:', err);
            },
            complete: async () => { // 确保 complete 回调是 async
              console.log('请求订阅消息(点赞通知) API 调用完成，继续提交留言');
              try {
                await sendRequestInternal();
              } catch (e) {
                // 捕获 sendRequestInternal 可能抛出的未处理异常
                this.handleSubmissionError(e.message || e);
              } finally {
                // 无论 sendRequestInternal 成功或失败，都隐藏loading
                uni.hideLoading();
              }
            }
          });
        } else {
          if (this.$platform === 'WX') {
            console.warn('未获取到点赞通知模板ID，跳过订阅消息请求，直接提交。');
          }
          // 非微信平台，或微信无模板ID，直接发送请求
          console.log('当前非微信平台或无模板ID，直接提交留言');
          try {
            await sendRequestInternal();
          } catch (e) {
            // 捕获 sendRequestInternal 可能抛出的未处理异常
            this.handleSubmissionError(e.message || e);
          } finally {
            // 无论 sendRequestInternal 成功或失败，都隐藏loading
            uni.hideLoading();
          }
        }
      } catch (error) {
        // 捕获 getOpenId 或其他同步错误
        this.handleSubmissionError(error.message || error);
        uni.hideLoading(); // 在这些错误情况下也隐藏 loading
      }
    },

    handleSubmitSuccess() {
		
	  // 让输入框失焦
	  uni.hideKeyboard();
	   
      uni.showToast({
        title: '留言提交成功',
        icon: 'success',
        duration: 1000
      })
      
      // 显示成功弹窗
      this.showSuccessModal = true
      
      // --- 新增：清空输入框内容 ---
      this.name = '';
      this.content = '';
      // --- 清空代码结束 ---
    },

    goToViewMessage() {
      // 关闭弹窗
      this.showSuccessModal = false
      // 跳转到查看留言页面，使用encodeURIComponent确保正确编码
      uni.redirectTo({
        url: `/pages/view/view?name=${encodeURIComponent(this.submittedName)}`
      })
    },

    handleSubmissionError(errorMessage) {
      // console.log('留言提交失败:', errorMessage) // 移除调试日志
      let userMessage = '提交失败，请稍后再试'
      let title = '提交失败'
      
      // 检查是否是对象格式，某些情况下errorMessage可能是包含code的对象
      if (typeof errorMessage === 'object' && errorMessage.code) {
        // 根据错误代码显示不同的提示
        switch(errorMessage.code) {
          case 'content_violation':
            userMessage = errorMessage.error || '留言内容包含违规信息，请修改后重试'
            title = '内容违规'
            break
          case 'user_banned':
            userMessage = errorMessage.error || '您的账号已被禁用，无法提交留言'
            title = '账号已禁用'
            break
          case 'session_timeout':
          case 'token_expired':
          case 'auth_error':
            userMessage = '认证已过期，请重新登录'
            // 如果是token过期，尝试重新登录
            this.token = null
            uni.removeStorageSync('mailbox_token')
            break
          case 'wx_session_timeout':
            userMessage = '微信认证信息可能已过期，请尝试重新登录后再试'
            this.token = null
            uni.removeStorageSync('mailbox_token')
            break
          case 'database_error':
            userMessage = '数据存储失败，请稍后再试'
            break
          default:
            userMessage = errorMessage.error || '提交失败，请稍后再试'
        }
      } else if (typeof errorMessage === 'string') {
        // 兼容旧的错误处理方式
        if (errorMessage.includes('违规内容') || errorMessage.includes('不适当信息')) {
          userMessage = errorMessage
          title = '内容违规'
        } else if (errorMessage.includes('禁用') || errorMessage.includes('封禁')) {
          userMessage = errorMessage
          title = '账号已禁用'
        } else if (errorMessage.includes('重新登录') || errorMessage.includes('认证')) {
          userMessage = errorMessage
          this.token = null
          uni.removeStorageSync('mailbox_token')
        }
      }
      
      uni.showModal({
        title: title,
        content: userMessage,
        showCancel: false
      })
    },

    getOpenId() {
      // 如果已有有效token，直接使用
      if (this.token) {
        // console.log('使用已有token'); // 移除调试日志
        return Promise.resolve();
      }
      
      return new Promise((resolve, reject) => {
        uni.login({
          success: (loginRes) => {
            if (loginRes.code) {
              this.requestOpenId(loginRes.code, resolve, reject)
            } else {
              reject(new Error('登录失败'))
            }
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    },

    requestOpenId(code, resolve, reject) {
      uni.request({
        url: `${this.$baseUrl}/mailbox/get_openid`,
        method: 'POST',
        data: { 
          code,
          platform: this.$platform
        },
        success: (response) => {
          // 检查响应中是否包含token
          if (response.data.token) {
            // 保存token到组件状态和本地存储
            this.token = response.data.token;
            uni.setStorageSync('mailbox_token', this.token);
            // console.log('成功获取并保存token'); // 移除调试日志
            resolve();
          } else {
            console.error('服务器响应中没有token', response.data);
            reject(new Error('未能获取认证信息'));
          }
        },
        fail: (err) => {
          console.error('请求获取token失败:', err);
          reject(err);
        }
      })
    },

    sendMessageToServer() {
      // 确保有token
      if (!this.token) {
        // console.error('没有有效的认证信息，无法发送消息'); // 移除调试日志
        return Promise.resolve({
          success: false,
          error: '认证失败，请重新登录',
          code: 'auth_error'
        });
      }
      
      return new Promise((resolve, reject) => {
        uni.request({
          url: `${this.$baseUrl}/mailbox/messages`,
          method: 'POST',
          // 在请求头中添加Authorization Bearer token
          header: {
            'Authorization': `Bearer ${this.token}`,
            'Content-Type': 'application/json'
          },
          data: {
            name: this.name,
            content: this.content,
            show_author: this.showAuthor, // 新增：发送是否显示作者信息的选项
            // 不再发送 nickName 和 avatarUrl
            // nickName: this.userInfo.nickName, 
            // avatarUrl: this.userInfo.avatarUrl
            // 不再发送openid和platform，因为它们已经包含在token中
          },
          success: (res) => {
            if (res.statusCode === 200) {
              resolve({ success: true, data: res.data });
            } else if (res.statusCode === 401) {
              // 处理token失效的情况
              console.error('Token失效，需要重新登录');
              // 清除无效的token
              this.token = null;
              uni.removeStorageSync('mailbox_token');
              
              resolve({ 
                success: false, 
                error: res.data.error || '认证已过期，请重新登录',
                code: 'token_expired'
              });
            } else {
              // 将完整的错误信息传递出去，包括错误代码
              resolve({ 
                success: false, 
                error: res.data.error || '服务器响应错误',
                code: res.data.code || 'unknown_error'
              });
            }
          },
          fail: (err) => {
            resolve({ 
              success: false, 
              error: '网络请求失败',
              code: 'network_error'
            });
          }
        });
      });
    },

    onInputFocus(inputName) {
      this.focusedInput = inputName
    },

    onInputBlur(inputName) {
      if (this.focusedInput === inputName) {
        this.focusedInput = ''
      }
    },

    onButtonTouchStart() {
      this.isButtonHovered = true
    },

    onButtonTouchEnd() {
      this.isButtonHovered = false
    },

    // --- 用户信息相关方法 (从 profile.vue 移植) ---
    formatAvatarUrl(avatarUrl) {
    	if (!avatarUrl) {
    		return '/static/default-avatar.png';
    	}
    	if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    		return avatarUrl;
    	}
    	if (avatarUrl.startsWith('/')) {
    		return this.$baseUrl + avatarUrl;
    	}
    	return avatarUrl;
    },

    onChooseAvatar(e) {
    	// 如果不显示署名，不允许更换头像
    	if (!this.showAuthor) {
    		uni.showToast({
    			title: '请先开启署名',
    			icon: 'none'
    		});
    		return;
    	}
    	const avatarUrl = e.detail.avatarUrl;
    	if (avatarUrl) {
    		this.updateProfile({
    			avatarTempPath: avatarUrl
    		});
    	}
    },

    onGetUserInfo(e) {
    	// 如果不显示署名，不允许获取用户信息
    	if (!this.showAuthor && this.$platform === 'QQ') {
    		uni.showToast({
    			title: '请先开启署名',
    			icon: 'none'
    		});
    		return;
    	}
    	if (this.$platform !== 'QQ') return;
    	if (e && e.detail && e.detail.userInfo) {
    		const {
    			nickName,
    			avatarUrl
    		} = e.detail.userInfo;
    		uni.showLoading({
    			title: '同步中...'
    		});
    		if (avatarUrl) {
    			uni.downloadFile({
    				url: avatarUrl,
    				success: res => {
    					if (res.statusCode === 200) {
    						this.updateProfile({
    							avatarTempPath: res.tempFilePath,
    							nickname: nickName
    						});
    					} else {
    						this.updateProfile({
    							nickname: nickName
    						});
    					}
    				},
    				fail: () => {
    					this.updateProfile({
    						nickname: nickName
    					});
    				},
    				complete: () => {
    					uni.hideLoading();
    				}
    			});
    		} else {
    			this.updateProfile({
    				nickname: nickName
    			});
    			uni.hideLoading();
    		}
    	} else {
    		uni.showToast({
    			title: '获取资料失败',
    			icon: 'none'
    		});
    	}
    },

    editNickname() {
    	// 如果不显示署名，不允许编辑昵称
    	if (!this.showAuthor) {
    		uni.showToast({
    			title: '请先开启署名',
    			icon: 'none'
    		});
    		return;
    	}
    	this.isEditingNickname = true;
    	this.tempNickname = this.userInfo.nickname || '';
    },

    onNicknameInput(e) {
    	this.tempNickname = e.detail.value;
    },

    onNicknameBlur() {
    	if (this.tempNickname && this.tempNickname !== this.userInfo.nickname) {
    		this.updateProfile({
    			nickname: this.tempNickname
    		});
    	}
    	this.isEditingNickname = false;
    },

    async updateProfile(dataToUpdate) {
    	if (!this.token) {
    		await this.getOpenId();
    	}

    	uni.showLoading({
    		title: '更新中...'
    	});

    	if (dataToUpdate.avatarTempPath) {
    		uni.uploadFile({
    			url: `${this.$baseUrl}/mailbox/profile`,
    			filePath: dataToUpdate.avatarTempPath,
    			name: 'avatarFile',
    			header: {
    				'Authorization': `Bearer ${this.token}`
    			},
    			formData: dataToUpdate.nickname ? {
    				nickname: dataToUpdate.nickname
    			} : {},
    			success: (uploadRes) => {
    				if (uploadRes.statusCode === 200) {
    					const result = JSON.parse(uploadRes.data);
    					let newAvatar = result.avatarUrl + (result.avatarUrl.includes('?') ? '&' : '?') + 'v=' + Date.now();
    					if (newAvatar.startsWith('http://')) newAvatar = newAvatar.replace('http://','https://');
    					this.userInfo.avatarUrl = newAvatar;
    					if (result.nickname) this.userInfo.nickname = result.nickname;
    					
    					const app = getApp();
    					app.globalData.userInfo = JSON.parse(JSON.stringify(this.userInfo));
    					if (typeof app.refreshUserInfo === 'function') {
    						app.refreshUserInfo();
    					}
    					uni.showToast({ title: '更新成功' });
    				} else {
    					uni.showToast({ title: '更新失败', icon: 'none' });
    				}
    			},
    			fail: () => uni.showToast({ title: '更新失败', icon: 'none' }),
    			complete: () => uni.hideLoading()
    		});
    	} else {
    		uni.request({
    			url: `${this.$baseUrl}/mailbox/profile`,
    			method: 'POST',
    			header: {
    				'Authorization': `Bearer ${this.token}`,
    				'Content-Type': 'application/json'
    			},
    			data: dataToUpdate,
    			success: (res) => {
    				if (res.statusCode === 200) {
    					if (res.data.nickname) this.userInfo.nickname = res.data.nickname;
    					if (res.data.avatarUrl) {
    						let newAvatar = res.data.avatarUrl + (res.data.avatarUrl.includes('?') ? '&' : '?') + 'v=' + Date.now();
    						if (newAvatar.startsWith('http://')) newAvatar = newAvatar.replace('http://','https://');
    						this.userInfo.avatarUrl = newAvatar;
    					}
    					
    					const app = getApp();
    					app.globalData.userInfo = JSON.parse(JSON.stringify(this.userInfo));
    					if (typeof app.refreshUserInfo === 'function') {
    						app.refreshUserInfo();
    					}
    					uni.showToast({ title: '更新成功' });
    				} else {
    					uni.showToast({ title: '更新失败', icon: 'none' });
    				}
    			},
    			fail: () => uni.showToast({ title: '更新失败', icon: 'none' }),
    			complete: () => uni.hideLoading()
    		});
    	}
    },
    // --- 用户信息方法结束 ---

    // 获取分享配置
    fetchShareConfig(pageKey) {
      const nameParam = this.name ? `&name=${encodeURIComponent(this.name)}` : '';
      uni.request({
        url: `${this.$baseUrl}/mailbox/share-config?pageKey=${pageKey}${nameParam}`,
        method: 'GET',
        header: this.token ? { 'Authorization': `Bearer ${this.token}` } : {},
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.success) {
            // 更新分享配置
            this.shareConfig = {
              title: res.data.title || this.shareConfig.title,
              imageUrl: res.data.imageUrl || this.shareConfig.imageUrl
            };
          } else {
            console.error(`分享配置请求返回不成功:`, res.data); // 保留错误日志
          }
        },
        fail: (err) => {
          console.error('获取分享配置失败:', err); // 保留错误日志
          // 失败时保留默认值
        }
      });
    },

    // 新增：获取应用配置
    fetchAppConfig() {
      uni.request({
        url: `${this.$baseUrl}/mailbox/config?platform=${this.$platform}`,
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data.success) {
            if (res.data.data.like_template_id) {
              this.likeTemplateId = res.data.data.like_template_id;
              console.log('成功获取点赞订阅消息模板ID:', this.likeTemplateId);
            }
          } else {
            console.error('获取应用配置失败:', res.data);
          }
        },
        fail: (err) => {
          console.error('请求应用配置接口失败:', err);
        }
      });
    },

    onShowAuthorChange(e) {
      this.showAuthor = e.detail.value;
    },

    showSignatureDisabledTip() {
      uni.showToast({
        title: '请先开启署名',
        icon: 'none'
      });
    },

    // 触发动画效果
    triggerAnimation() {
      this.animating = true;
      setTimeout(() => {
        this.animating = false;
      }, 600); // 稍微增加动画持续时间，确保完全结束
    }
  }
}
</script>

<style>
/* 基础容器样式 */
.container {
  min-height: 100vh;
  height: 100%; 
  background: linear-gradient(180deg, #fdfbfb 0%, #f6f7fb 100%);
  background-attachment: fixed; 
  position: relative;
  overflow-x: hidden; 
  overflow-y: auto;
  padding-top: var(--status-bar-height);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* 背景效果 */
.gradient-blob {
  position: fixed;
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(80rpx);
  z-index: 0;
  will-change: transform, opacity, border-radius;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.blob-1 {
  width: 500rpx;
  height: 450rpx;
  background: radial-gradient(circle, rgba(255, 111, 145, 0.5) 0%, rgba(254, 172, 94, 0.3) 100%);
  top: -150rpx;
  left: -100rpx;
  animation: blobMorph 20s cubic-bezier(0.45, 0.05, 0.55, 0.95) infinite alternate;
}

.blob-2 {
  width: 600rpx;
  height: 550rpx;
  background: radial-gradient(circle, rgba(79, 95, 232, 0.4) 0%, rgba(168, 209, 242, 0.2) 100%);
  bottom: -200rpx;
  right: -150rpx;
  animation: blobMorph 25s cubic-bezier(0.37, 0, 0.63, 1) infinite alternate-reverse 2s;
}

.blob-3 {
  width: 400rpx;
  height: 380rpx;
  background: radial-gradient(circle, rgba(132, 250, 176, 0.4) 0%, rgba(143, 211, 244, 0.2) 100%);
  top: 30%;
  right: 5%;
  animation: blobMorph 18s cubic-bezier(0.65, 0.05, 0.36, 1) infinite alternate 1s;
}

@keyframes blobMorph {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
    border-radius: 50% 45% 55% 48%;
    opacity: 0.3;
  }
  25% {
    transform: translate3d(80rpx, -100rpx, 0) scale(1.1) rotate(45deg);
    border-radius: 45% 55% 48% 50%;
    opacity: 0.4;
  }
  50% {
    transform: translate3d(-60rpx, 90rpx, 0) scale(0.9) rotate(-30deg);
    border-radius: 55% 48% 50% 45%;
    opacity: 0.25;
  }
  75% {
    transform: translate3d(90rpx, 120rpx, 0) scale(1.05) rotate(90deg);
    border-radius: 48% 50% 45% 55%;
    opacity: 0.35;
  }
}

.decorative-pattern {
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  background-image: radial-gradient(rgba(0, 0, 0, 0.08) 0.5px, transparent 0.5px);
  background-size: 15rpx 15rpx;
  opacity: 0.5;
  z-index: 0;
  animation: patternPulse 15s ease-in-out infinite;
}

@keyframes patternPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.15; }
}

/* 内容区域 */
.content {
  padding: 20rpx 30rpx;
  z-index: 1;
  position: relative;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

/* 头部样式 */
.header {
  margin-bottom: 30rpx;
  text-align: center;
  background: linear-gradient(135deg, rgba(168, 230, 207, 0.8) 0%, rgba(220, 237, 193, 0.8) 100%);
  padding: 40rpx 0;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.4);
  z-index: -1;
}

.title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2a9d8f;
  text-shadow: 0 2rpx 3rpx rgba(0, 0, 0, 0.1);
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  left: 50%;
  bottom: -10rpx;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(90deg, #a8e6cf, #dcedc1);
  border-radius: 3rpx;
  transform: translateX(-50%);
}

.subtitle {
  font-size: 24rpx;
  color: rgba(42, 157, 143, 0.9);
  margin-top: 20rpx;
  display: block;
}

/* 安全提示样式 */
.safety-tip {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(231, 76, 60, 0.08);
  border-radius: 20rpx;
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
  border: 1rpx solid rgba(231, 76, 60, 0.2);
}

.safety-icon {
  flex-shrink: 0;
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
  margin-top: 4rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23e74c3c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z'%3E%3C/path%3E%3Cline x1='12' y1='9' x2='12' y2='13'%3E%3C/line%3E%3Cline x1='12' y1='17' x2='12.01' y2='17'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
}

.safety-tip-text {
  font-size: 24rpx;
  color: #e74c3c;
  line-height: 1.5;
}

/* 表单容器 */
.form-container {
  width: 100%;
  margin-bottom: 30rpx;
}

.form {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 30rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.6);
}

/* 输入框组样式 */
.input-group {
  position: relative;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.input-icon {
  position: absolute;
  left: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 28rpx;
  height: 28rpx;
  background-size: contain;
  z-index: 1;
}

.name-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232a9d8f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2'%3E%3C/path%3E%3Ccircle cx='12' cy='7' r='4'%3E%3C/circle%3E%3C/svg%3E");
}

.message-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232a9d8f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z'%3E%3C/path%3E%3C/svg%3E");
  top: 30rpx;
  transform: none;
}

.input, .textarea {
  width: 100%;
  box-sizing: border-box;
  padding: 0 20rpx 0 60rpx;
  line-height: 90rpx;
  border: 1rpx solid rgba(42, 157, 143, 0.3);
  border-radius: 16rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.input {
  height: 90rpx; /* 明确设置输入框高度 */
}

.textarea {
  height: 400rpx; /* 增加文本域高度 */
  resize: none;
  padding-top: 30rpx;
  line-height: 1.5; /* 添加行高以改善文本显示 */
  min-height: 150rpx; /* 设置最小高度 */
}

.input-focus, .input:focus, .textarea:focus {
  border-color: #a8e6cf;
  background-color: #fff;
  box-shadow: 0 0 20rpx rgba(168, 230, 207, 0.2);
  outline: none;
}

/* 新增：输入框底部边框动画效果 */
.input-border {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #a8e6cf, #dcedc1);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
  border-radius: 3rpx;
}

.input-border-active {
  width: 100%;
  opacity: 1;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #a8e6cf, #dcedc1);
  color: #2a9d8f;
  transition: all 0.3s ease;
  margin-top: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(168, 230, 207, 0.3);
  position: relative;
  overflow: hidden;
  border: none;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: 0.5s;
}

.submit-button:active::before {
  left: 100%;
}

/* 新增：按钮点击波纹特效 */
.button-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  animation: ripple 0.6s linear;
}

@keyframes ripple {
  0% {
    width: 10rpx;
    height: 10rpx;
    opacity: 0.5;
  }
  100% {
    width: 500rpx;
    height: 500rpx;
    opacity: 0;
  }
}

.button-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232a9d8f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='22' y1='2' x2='11' y2='13'%3E%3C/line%3E%3Cpolygon points='22 2 15 22 11 13 2 9 22 2'%3E%3C/polygon%3E%3C/svg%3E");
  background-size: cover;
}

.button-hover {
  transform: scale(0.97);
  box-shadow: 0 4rpx 10rpx rgba(168, 230, 207, 0.2);
}

.button-text {
  color: #2a9d8f;
  font-size: 30rpx;
  font-weight: 600;
}

/* 成功弹窗样式 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5rpx);
  -webkit-backdrop-filter: blur(5rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99999;
  animation: fadeIn 0.3s ease;
}

/* 新增：弹窗背景点击区域 */
.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  width: 80%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 100000;
  animation: scaleIn 0.4s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

@keyframes scaleIn {
  from { 
    transform: scale(0.8);
    opacity: 0;
  }
  to { 
    transform: scale(1);
    opacity: 1;
  }
}

.success-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 20rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232a9d8f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: cover;
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2a9d8f;
  margin-bottom: 10rpx;
  display: block;
}

.modal-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-button {
  width: 100%;
  height: 90rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.view-button {
  background: linear-gradient(135deg, #a8e6cf, #dcedc1);
  box-shadow: 0 6rpx 15rpx rgba(168, 230, 207, 0.3);
}

.view-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%232a9d8f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E");
  background-size: cover;
}

.share-button {
  background: linear-gradient(135deg, #ff9a9e, #fad0c4);
  box-shadow: 0 6rpx 15rpx rgba(255, 154, 158, 0.3);
}

.share-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ff758c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='18' cy='5' r='3'%3E%3C/circle%3E%3Ccircle cx='6' cy='12' r='3'%3E%3C/circle%3E%3Ccircle cx='18' cy='19' r='3'%3E%3C/circle%3E%3Cline x1='8.59' y1='13.51' x2='15.42' y2='17.49'%3E%3C/line%3E%3Cline x1='15.41' y1='6.51' x2='8.59' y2='10.49'%3E%3C/line%3E%3C/svg%3E");
  background-size: cover;
}

.action-button:active {
  transform: scale(0.97);
  opacity: 0.9;
}

/* 动画效果 - 移除了页面初始动效 */

.animate-bounce {
  animation: bounce 1s ease infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8rpx 20rpx rgba(168, 230, 207, 0.3);
  }
  50% {
    box-shadow: 0 8rpx 30rpx rgba(168, 230, 207, 0.5);
  }
  100% {
    box-shadow: 0 8rpx 20rpx rgba(168, 230, 207, 0.3);
  }
}

.animate-appear {
  animation: appearUp 0.4s ease forwards;
  opacity: 0;
  transform: translateY(20rpx);
}

@keyframes appearUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 新增：显示作者信息选项样式 - 移除旧样式 */
.author-option {
  display: none;
}

/* 新增：签名卡片样式 */
.signature-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  border: 1rpx solid rgba(42, 157, 143, 0.2);
  transition: background-color 0.5s ease; /* 简化过渡效果 */
}

.signature-left {
  display: flex;
  align-items: center;
}

.avatar-button {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  padding: 0;
  margin: 0 20rpx 0 0;
  border: none;
  background-color: transparent;
  flex-shrink: 0;
  position: relative;
  overflow: visible;
}

.avatar-button::after {
  border: none;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2rpx solid rgba(42, 157, 143, 0.3);
  transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1); /* 仅对变形应用过渡 */
  will-change: transform, opacity; /* 优化性能 */
}

.nickname-section {
  display: flex;
  flex-direction: column;
}

.nickname-text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1); /* 仅对变形应用过渡 */
  will-change: transform, opacity; /* 优化性能 */
}

.nickname-prompt {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
  transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1); /* 仅对变形应用过渡 */
  will-change: transform, opacity; /* 优化性能 */
}

.nickname-input {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	border-bottom: 2rpx solid #2a9d8f;
	padding: 4rpx 0;
}

.signature-right {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.switch-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
}

/* 新增：编辑指示器样式 */
.avatar-edit-indicator {
	position: absolute;
	right: -6rpx;
	bottom: -6rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #2a9d8f; /* 主题绿色 */
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
  border: 2rpx solid #fff;
  z-index: 5;
}

.edit-icon {
	width: 18rpx;
	height: 18rpx;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z'%3E%3C/path%3E%3C/svg%3E");
	background-size: contain;
	background-repeat: no-repeat;
	background-position: center;
}

/* 禁用状态样式 */
.avatar-button.disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

.nickname-section.disabled {
  opacity: 0.8;
  cursor: not-allowed;
}

/* 编辑指示器隐藏状态 */
.avatar-edit-indicator.hidden {
  opacity: 0;
  transform: scale(0.5);
}

/* 添加动画效果 */
@keyframes fadeTransition {
  0% {
    opacity: 0.7;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 添加更强的过渡动画 */
@keyframes pulseAnimation {
  0% {
    opacity: 0.95;
    transform: scale(0.92);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画 */
.avatar-image, .nickname-text, .nickname-prompt {
  /* 移除默认动画，避免与状态切换动画冲突 */
}

/* 应用增强动画 */
.avatar-image.animating, 
.nickname-text.animating, 
.nickname-prompt.animating {
  animation: pulseAnimation 0.5s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards;
  /* 添加forwards确保动画结束时保持最终状态 */
}

/* 确保编辑指示器有过渡效果 */
.avatar-edit-indicator {
  transition: opacity 0.3s ease, transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: opacity, transform;
}
</style>