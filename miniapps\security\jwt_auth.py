"""
JWT 认证工具模块

提供JWT Token的生成、验证和相关装饰器功能
"""
import os
import logging
import datetime
import jwt
import secrets
from functools import wraps
from flask import request, jsonify
from dotenv import load_dotenv
import pymysql.cursors

# 确保环境变量已加载
load_dotenv()

# 获取日志记录器
logger = logging.getLogger(__name__)

# JWT 密钥
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')
if not JWT_SECRET_KEY:
    logger.error("JWT_SECRET_KEY未配置！生成临时密钥，请尽快在.env中配置正式密钥！")
    JWT_SECRET_KEY = secrets.token_hex(32)  # 生成更强的随机密钥
    print(f"警告：使用临时生成的JWT密钥：{JWT_SECRET_KEY}。请在.env文件中设置JWT_SECRET_KEY！")

# Token默认过期时间（从环境变量获取，默认7天）
DEFAULT_EXPIRATION_DAYS = int(os.getenv('JWT_EXPIRATION_DAYS', '7'))

# --- 导入连接池模块的获取连接函数 ---
from config.connection_pools import get_mailbox_db_connection

def generate_token(payload, expiration_days=None):
    """
    生成JWT Token
    
    参数:
        payload (dict): Token中要包含的数据载荷
        expiration_days (int): Token过期天数，如果为None则使用配置的默认值
        
    返回:
        str: 生成的JWT Token
    """
    try:
        # 使用传入的过期时间或默认值
        exp_days = expiration_days if expiration_days is not None else DEFAULT_EXPIRATION_DAYS
        
        # 添加过期时间
        payload['exp'] = datetime.datetime.utcnow() + datetime.timedelta(days=exp_days)
        # 可选: 添加签发时间
        # payload['iat'] = datetime.datetime.utcnow()
        
        # 生成Token
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm='HS256')
        logger.info(f"Token生成成功 - 用户: {payload.get('openid')}, Platform: {payload.get('platform')}")
        return token
    except Exception as e:
        logger.error(f"生成Token失败: {str(e)}")
        raise

def decode_token(token):
    """
    解码JWT Token
    
    参数:
        token (str): 要解码的JWT token
        
    返回:
        dict: 解码后的数据载荷
        
    异常:
        jwt.ExpiredSignatureError: Token已过期
        jwt.InvalidTokenError: Token无效
    """
    # 注意：decode 函数本身就会验证过期时间、签名等
    return jwt.decode(token, JWT_SECRET_KEY, algorithms=['HS256'])

def token_required(f):
    """
    JWT Token验证装饰器
    用于保护需要认证的API端点
    
    将在通过验证后，把用户信息(包括openid, platform, id)附加到request.current_user
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        # 检查 Authorization header 是否存在且格式正确 (Bearer <token>)
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            parts = auth_header.split()
            if len(parts) == 2 and parts[0].lower() == 'bearer':
                token = parts[1]
            else:
                 logger.warning(f"Authorization Header 格式错误: {auth_header}")

        if not token:
            logger.warning("请求缺少 Token")
            return jsonify({'error': '需要提供 Token 进行认证'}), 401 # Unauthorized

        try:
            # 1. 验证 token (签名和过期时间)
            data = decode_token(token)
            
            # --- 获取用户ID (现在从集中管理的连接池获取连接) ---
            openid = data.get('openid')
            platform = data.get('platform')
            
            if not openid or not platform:
                 logger.error(f"Token载荷缺少openid或platform")
                 return jsonify({'error': '无效的 Token 载荷'}), 401
            
            user_id = None
            connection = None
            try:
                connection = get_mailbox_db_connection() # 使用集中管理的Mailbox连接池
                with connection.cursor() as cursor:
                    cursor.execute(
                        "SELECT id FROM users WHERE openid = %s AND platform = %s",
                        (openid, platform)
                    )
                    user = cursor.fetchone()
                    if user:
                        user_id = user['id']
                        data['id'] = user_id # 将用户ID添加到数据中
                        logger.info(f"Token 验证成功，UserID: {user_id}, Platform: {platform}")
                    else:
                        # Token 有效，但数据库中找不到对应的用户记录 (可能被删除了？)
                        logger.error(f"Token有效但未找到用户记录: OpenID: {openid}, Platform: {platform}")
                        # 返回 401 或 404 都可以，这里用 401 表示认证失败
                        return jsonify({'error': '认证用户不存在，请重新登录'}), 401 
            except pymysql.Error as db_err:
                 logger.error(f"Token验证时数据库查询失败: {str(db_err)}")
                 return jsonify({'error': '用户认证数据库查询失败'}), 500
            except ConnectionError as conn_err: # 捕获连接池错误
                 logger.error(f"Token验证时无法获取数据库连接: {str(conn_err)}")
                 return jsonify({'error': '用户认证服务暂时不可用'}), 503 # Service Unavailable
            finally:
                 if connection:
                    connection.close() # 归还连接到池
            # --- 用户ID获取结束 ---
            
            # 将包含 id 的用户信息附加到请求上下文
            request.current_user = data 
            
        except jwt.ExpiredSignatureError:
            logger.warning("Token 已过期")
            return jsonify({'error': 'Token 已过期，请重新登录'}), 401 # Unauthorized
        except jwt.InvalidTokenError as e:
            logger.error(f"无效的 Token: {str(e)}")
            return jsonify({'error': '无效的 Token'}), 401 # Unauthorized
        except Exception as e:
             # 捕获 decode_token 或其他未预料的错误
             logger.error(f"Token 验证时发生未知错误: {str(e)}")
             return jsonify({'error': 'Token 验证失败'}), 500

        # Token 验证通过且用户ID已获取，执行被装饰的视图函数
        return f(*args, **kwargs)
    return decorated 