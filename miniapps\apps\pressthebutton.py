import logging
import requests
from flask import Flask, request, jsonify, render_template_string
import pymysql
import random
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

app = Flask(__name__)

# QQ小程序的 appid 和 secret
QQ_APPID = os.getenv('PRESSTHEBUTTON_QQ_APPID')
QQ_SECRET = os.getenv('PRESSTHEBUTTON_QQ_SECRET')

# 数据库连接
from config.database import PRESS_BUTTON_DB_CONFIG
def get_db_connection():
    """创建数据库连接"""
    try:
        connection = pymysql.connect(**PRESS_BUTTON_DB_CONFIG)
        logger.info("数据库连接成功")
        return connection
    except pymysql.Error as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise ConnectionError(f"无法连接到数据库: {str(e)}")


from security.content_security import ContentSecurity
from security.content_security_wx import ContentSecurityWX
# 初始化 ContentSecurity
content_security = ContentSecurity(QQ_APPID, QQ_SECRET)


# HTML 模板
dashboard_html = """
<!-- 这里是您的仪表盘HTML模板 -->
pressthebutton仪表盘
"""

# API路由
@app.route('/pressthebutton/')
def home():
    """渲染主页"""
    # logger.info("访问主页")
    return render_template_string(dashboard_html)

def get_openid_from_qq(code):
    url = f'https://api.q.qq.com/sns/jscode2session'
    params = {
        'appid': QQ_APPID,
        'secret': QQ_SECRET,
        'js_code': code,
        'grant_type': 'authorization_code'
    }
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        if 'openid' in data:
            return data['openid']
        else:
            raise Exception(f"Failed to get openid: {data.get('errmsg', 'Unknown error')}")
    else:
        raise Exception(f"QQ API request failed with status code {response.status_code}")

@app.route('/pressthebutton/login', methods=['POST'])
def login():
    data = request.json
    code = data.get('code')
    nickname = data.get('nickname')
    avatar_url = data.get('avatar_url')

    if not code:
        return jsonify({'success': False, 'error': 'Missing code'}), 400

    try:
        openid = get_openid_from_qq(code)
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 检查用户是否存在
            cursor.execute("SELECT id FROM users WHERE openid = %s", (openid,))
            user = cursor.fetchone()

            if user:
                # 用户存在，更新信息
                cursor.execute("UPDATE users SET nickname = %s, avatar_url = %s WHERE openid = %s",
                               (nickname, avatar_url, openid))
            else:
                # 新用户，插入
                cursor.execute("INSERT INTO users (openid, nickname, avatar_url) VALUES (%s, %s, %s)",
                               (openid, nickname, avatar_url))

            conn.commit()

            # 获取用户 id
            cursor.execute("SELECT id FROM users WHERE openid = %s", (openid,))
            user = cursor.fetchone()

            return jsonify({'success': True, 'userId': user['id']})
    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500
    finally:
        conn.close()


@app.route('/pressthebutton/scenario', methods=['GET'])
def get_random_scenario():
    user_id = request.args.get('userId')
    if not user_id:
        return jsonify({'error': 'Missing userId'}), 400

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 获取用户未选择过的场景
            cursor.execute("""
                SELECT s.id, s.scenario_condition as `condition`, s.result 
                FROM scenarios s
                LEFT JOIN user_choices uc ON s.id = uc.scenario_id AND uc.user_id = %s
                WHERE uc.id IS NULL
                ORDER BY RAND() 
                LIMIT 1
            """, (user_id,))
            scenario = cursor.fetchone()

            if scenario:
                return jsonify(scenario)
            else:
                return jsonify({'noMoreScenarios': True})
    finally:
        conn.close()


@app.route('/pressthebutton/choice', methods=['POST'])
def make_choice():
    data = request.json
    scenario_id = data['scenarioId']
    choice = data['choice']
    user_id = data['userId']

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                "INSERT INTO user_choices (user_id, scenario_id, choice) VALUES (%s, %s, %s) ON DUPLICATE KEY UPDATE choice = %s",
                (user_id, scenario_id, choice, choice))

            cursor.execute("UPDATE scenarios SET {} = {} + 1 WHERE id = %s".format(choice, choice), (scenario_id,))
            conn.commit()

            # 获取总投票数和百分比
            cursor.execute("SELECT press, decline FROM scenarios WHERE id = %s", (scenario_id,))
            result = cursor.fetchone()
            total = result['press'] + result['decline']
            press_percentage = (result['press'] / total) * 100
            decline_percentage = (result['decline'] / total) * 100

            return jsonify({
                'pressPercentage': press_percentage,
                'declinePercentage': decline_percentage,
                'totalVotes': total,  # 添加总投票数
                'pressCount': result['press'],  # 添加具体按下人数
                'declineCount': result['decline']  # 添加具体不按人数
            })
    finally:
        conn.close()


@app.route('/pressthebutton/comments', methods=['GET'])
def get_comments():
    scenario_id = request.args.get('scenarioId')

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # 获取所有评论
            cursor.execute("""
                SELECT c.*, u.nickname, u.avatar_url 
                FROM comments c
                JOIN users u ON c.user_id = u.id
                WHERE c.scenario_id = %s 
                ORDER BY c.created_at ASC
            """, (scenario_id,))
            all_comments = cursor.fetchall()

            # 构建评论树
            comment_dict = {}
            top_level_comments = []
            for comment in all_comments:
                comment['replies'] = []
                comment_dict[comment['id']] = comment
                if comment['parent_id'] is None:
                    top_level_comments.append(comment)
                else:
                    parent = comment_dict.get(comment['parent_id'])
                    if parent:
                        parent['replies'].append(comment)

            return jsonify({'comments': top_level_comments})
    finally:
        conn.close()


@app.route('/pressthebutton/comment', methods=['POST'])
def add_comment():
    data = request.json
    scenario_id = data['scenarioId']
    content = data['content']
    user_id = data['userId']
    parent_id = data.get('parentId')  # 对于顶级评论，这可能是None

    # 内容安全检查
    try:
        if not content_security.msg_sec_check(content):
            # logger.warning("评论内容包含不当内容")
            return jsonify({'success': False, 'error': 'Comment contains inappropriate content'}), 400
    except Exception as e:
        # logger.error(f"内容安全检查失败: {str(e)}")
        return jsonify({'success': False, 'error': 'Content security check failed'}), 500

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                "INSERT INTO comments (scenario_id, content, user_id, parent_id) VALUES (%s, %s, %s, %s)",
                (scenario_id, content, user_id, parent_id)
            )
            conn.commit()

            # 获取新创建的评论
            cursor.execute("""
                SELECT c.*, u.nickname, u.avatar_url 
                FROM comments c
                JOIN users u ON c.user_id = u.id
                WHERE c.id = LAST_INSERT_ID()
            """)
            new_comment = cursor.fetchone()

            return jsonify({'success': True, 'comment': new_comment})
    finally:
        conn.close()


if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)