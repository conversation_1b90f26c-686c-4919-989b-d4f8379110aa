from flask import Flask, jsonify
from flask_cors import CORS

# 导入三个项目的代码
from apps.mailbox import app as mailbox_app
from apps.pressthebutton import app as pressthebutton_app


def create_app():
    app = Flask(__name__)
    CORS(app)

    # 根路由 - 显示可用端点
    @app.route('/')
    def index():
        return """
        <h1>Mini Apps Server</h1>
        <p>Available endpoints:</p>
        <ul>
            <li><a href="/mailbox">/mailbox</a></li>
            <li><a href="/pressthebutton">/pressthebutton</a></li>
        </ul>
        <p><a href="/debug/routes">View all routes</a></p>
        """

    # 注册路由
    for rule in mailbox_app.url_map.iter_rules():
        endpoint = f'mailbox_{rule.endpoint}'
        path = rule.rule if rule.rule.startswith('/mailbox') else f'/mailbox{rule.rule}'
        app.add_url_rule(path, endpoint, mailbox_app.view_functions[rule.endpoint], methods=rule.methods)

    for rule in pressthebutton_app.url_map.iter_rules():
        endpoint = f'pressthebutton_{rule.endpoint}'
        path = rule.rule if rule.rule.startswith('/pressthebutton') else f'/pressthebutton{rule.rule}'
        app.add_url_rule(path, endpoint, pressthebutton_app.view_functions[rule.endpoint], methods=rule.methods)

    # 调试路由
    @app.route('/debug/routes')
    def list_routes():
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'path': rule.rule
            })
        return jsonify(routes)

    return app


if __name__ == '__main__':
    app = create_app()
    app.run(host='127.0.0.1', port=5000, debug=True)